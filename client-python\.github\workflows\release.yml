name: release
on:
  push:
    tags:
      - v*
permissions:
  contents: read
jobs:
  test:
    runs-on: ubuntu-latest
    name: Release to PyPi
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v3
        with:
          python-version: "3.10"
      - name: Setup Poetry
        uses: abatilo/actions-poetry@v2
      - name: Configure Poetry
        run: poetry config pypi-token.pypi ${{ secrets.POETRY_HTTP_BASIC_PYPI_PASSWORD }}
      - name: Install pypi deps
        run: poetry install
      - name: Get tag
        id: tag
        uses: dawidd6/action-get-tag@v1
      - name: Version according to tag
        run: poetry version ${{ steps.tag.outputs.tag }}
      - name: Build
        run: poetry build
      - name: Publish to PyPi
        run: poetry publish
