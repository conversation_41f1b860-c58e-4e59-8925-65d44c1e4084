"""
Real Options Flow Analyzer with Polygon.io Integration
Inspired by the Polygon.io client repository examples

This version uses actual Polygon.io API calls when API key is available.
"""

import os
import gradio as gr
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

try:
    from polygon import RESTClient
    POLYGON_AVAILABLE = True
except ImportError:
    POLYGON_AVAILABLE = False
    print("Polygon client not available. Install with: pip install polygon-api-client")

class OptionsAnalyzer:
    def __init__(self):
        self.api_key = os.getenv('POLYGON_API_KEY')
        if self.api_key and POLYGON_AVAILABLE:
            self.client = RESTClient(self.api_key)
            self.real_data = True
            print("✅ Connected to Polygon.io API")
        else:
            self.client = None
            self.real_data = False
            print("⚠️ Using demo data. Set POLYGON_API_KEY for real data.")

    def get_current_price(self, symbol):
        """Get current stock price"""
        if self.real_data:
            try:
                # Get latest quote
                quote = self.client.get_last_quote(symbol)
                return (quote.bid + quote.ask) / 2 if quote else 150.0
            except:
                return 150.0
        return 150.0  # Mock price

    def get_options_chain_real(self, symbol, expiry_date=None):
        """Get real options chain from Polygon.io"""
        try:
            options_data = []

            # Get options chain snapshot
            for option in self.client.list_snapshot_options_chain(
                symbol,
                params={
                    "expiration_date.gte": (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d"),
                    "limit": 100
                }
            ):
                if hasattr(option, 'details') and hasattr(option, 'last_quote'):
                    strike = option.details.strike_price
                    option_type = option.details.contract_type

                    # Get volume and Greeks if available
                    volume = getattr(option.session, 'volume', 0) if hasattr(option, 'session') else 0
                    iv = getattr(option.implied_volatility, 'value', 0.2) if hasattr(option, 'implied_volatility') else 0.2
                    delta = getattr(option.greeks, 'delta', 0) if hasattr(option, 'greeks') else 0

                    price = option.last_quote.ask if option.last_quote else 0

                    options_data.append({
                        'strike': strike,
                        'type': option_type,
                        'volume': volume,
                        'iv': iv,
                        'delta': delta,
                        'price': price
                    })

            if options_data:
                df = pd.DataFrame(options_data)

                # Pivot to separate calls and puts
                calls = df[df['type'] == 'call'].copy()
                puts = df[df['type'] == 'put'].copy()

                # Merge on strike price
                result = pd.merge(calls[['strike', 'volume', 'iv', 'delta', 'price']],
                                puts[['strike', 'volume', 'iv', 'delta', 'price']],
                                on='strike', suffixes=('_call', '_put'), how='outer').fillna(0)

                result.columns = ['strike', 'call_volume', 'call_iv', 'call_delta', 'call_price',
                                'put_volume', 'put_iv', 'put_delta', 'put_price']

                return result.sort_values('strike')

        except Exception as e:
            print(f"Error fetching real options data: {e}")

        # Fallback to mock data
        return self.get_options_chain_mock(symbol)

    def get_options_chain_mock(self, symbol):
        """Generate mock options chain data"""
        np.random.seed(hash(symbol) % 1000)  # Consistent data per symbol

        current_price = self.get_current_price(symbol)
        strikes = np.arange(
            current_price * 0.8,
            current_price * 1.2,
            current_price * 0.025
        )
        strikes = np.round(strikes / 2.5) * 2.5  # Round to nearest 2.5

        options_data = []
        for strike in strikes:
            moneyness = strike / current_price

            # More realistic Greeks calculation
            call_delta = max(0.01, min(0.99,
                0.5 + (current_price - strike) / (current_price * 0.2)))
            put_delta = call_delta - 1.0

            # Volume with realistic patterns
            base_volume = np.random.lognormal(4, 1)
            if abs(strike - current_price) < current_price * 0.05:  # ATM
                volume_multiplier = np.random.uniform(2, 5)
            else:
                volume_multiplier = np.random.uniform(0.3, 1.5)

            call_volume = int(base_volume * volume_multiplier)
            put_volume = int(base_volume * volume_multiplier * np.random.uniform(0.7, 1.3))

            # IV smile
            atm_iv = 0.25
            iv_skew = abs(moneyness - 1) * 0.3
            call_iv = atm_iv + iv_skew + np.random.normal(0, 0.02)
            put_iv = call_iv + np.random.normal(0, 0.01)

            options_data.append({
                'strike': strike,
                'call_volume': call_volume,
                'put_volume': put_volume,
                'call_delta': call_delta,
                'put_delta': put_delta,
                'call_iv': max(0.05, call_iv),
                'put_iv': max(0.05, put_iv),
                'call_price': max(0.01, current_price - strike + np.random.normal(0, 1)),
                'put_price': max(0.01, strike - current_price + np.random.normal(0, 1))
            })

        return pd.DataFrame(options_data)

    def get_options_chain(self, symbol):
        """Get options chain (real or mock)"""
        if self.real_data:
            return self.get_options_chain_real(symbol)
        else:
            return self.get_options_chain_mock(symbol)

    def detect_unusual_activity(self, chain_df, threshold=2.0):
        """Detect unusual options activity using statistical analysis"""
        if len(chain_df) == 0:
            return pd.DataFrame()

        # Calculate volume statistics
        call_vol_mean = chain_df['call_volume'].mean()
        call_vol_std = chain_df['call_volume'].std()
        put_vol_mean = chain_df['put_volume'].mean()
        put_vol_std = chain_df['put_volume'].std()

        if call_vol_std == 0 or put_vol_std == 0:
            return pd.DataFrame()

        # Calculate Z-scores
        chain_df = chain_df.copy()
        chain_df['call_vol_zscore'] = (chain_df['call_volume'] - call_vol_mean) / call_vol_std
        chain_df['put_vol_zscore'] = (chain_df['put_volume'] - put_vol_mean) / put_vol_std

        # Flag unusual activity
        unusual_mask = (chain_df['call_vol_zscore'] > threshold) | (chain_df['put_vol_zscore'] > threshold)
        unusual = chain_df[unusual_mask].copy()

        if len(unusual) > 0:
            unusual['activity_type'] = unusual.apply(
                lambda row: f"🔥 Unusual Call Volume (Z={row['call_vol_zscore']:.1f})"
                if row['call_vol_zscore'] > row['put_vol_zscore']
                else f"🔥 Unusual Put Volume (Z={row['put_vol_zscore']:.1f})", axis=1
            )

            unusual['max_zscore'] = unusual[['call_vol_zscore', 'put_vol_zscore']].max(axis=1)
            return unusual.sort_values('max_zscore', ascending=False)

        return pd.DataFrame()

# Initialize analyzer
analyzer = OptionsAnalyzer()

def analyze_options_comprehensive(symbol, show_unusual_only=False, volume_threshold=2.0):
    """Comprehensive options analysis"""
    try:
        symbol = symbol.upper().strip()
        if not symbol:
            return "Please enter a valid symbol", {}, {}, pd.DataFrame()

        # Get data
        chain = analyzer.get_options_chain(symbol)
        current_price = analyzer.get_current_price(symbol)

        if len(chain) == 0:
            return f"No options data available for {symbol}", {}, {}, pd.DataFrame()

        # Detect unusual activity
        unusual = analyzer.detect_unusual_activity(chain, volume_threshold)

        # Calculate key metrics
        atm_idx = (chain['strike'] - current_price).abs().idxmin()
        atm_call_iv = chain.loc[atm_idx, 'call_iv']
        atm_put_iv = chain.loc[atm_idx, 'put_iv']
        atm_iv = (atm_call_iv + atm_put_iv) / 2

        # Expected move (30-day approximation)
        expected_move = current_price * atm_iv * np.sqrt(30/365)

        # Choose display data
        if show_unusual_only and len(unusual) > 0:
            display_data = unusual
            title_suffix = " - 🔥 Unusual Activity"
        else:
            display_data = chain
            title_suffix = ""

        # Create enhanced volume chart
        fig_volume = go.Figure()

        # Add volume bars
        fig_volume.add_trace(go.Bar(
            x=display_data['strike'],
            y=display_data['call_volume'],
            name='📞 Call Volume',
            marker_color='rgba(0, 255, 0, 0.7)',
            hovertemplate='<b>Strike:</b> $%{x}<br><b>Call Volume:</b> %{y:,}<extra></extra>'
        ))

        fig_volume.add_trace(go.Bar(
            x=display_data['strike'],
            y=-display_data['put_volume'],
            name='📉 Put Volume',
            marker_color='rgba(255, 0, 0, 0.7)',
            hovertemplate='<b>Strike:</b> $%{x}<br><b>Put Volume:</b> %{y:,}<extra></extra>'
        ))

        # Add current price and expected move lines
        fig_volume.add_vline(x=current_price, line_dash="dash", line_color="yellow",
                           annotation_text=f"Current: ${current_price:.2f}")
        fig_volume.add_vline(x=current_price + expected_move, line_dash="dot", line_color="orange",
                           annotation_text=f"+1σ: ${current_price + expected_move:.2f}")
        fig_volume.add_vline(x=current_price - expected_move, line_dash="dot", line_color="orange",
                           annotation_text=f"-1σ: ${current_price - expected_move:.2f}")

        fig_volume.update_layout(
            title=f"📊 {symbol} Options Volume{title_suffix}",
            xaxis_title="Strike Price ($)",
            yaxis_title="Volume",
            template="plotly_dark",
            height=600,
            barmode='relative',
            showlegend=True
        )

        # Enhanced Greeks chart
        fig_greeks = go.Figure()

        fig_greeks.add_trace(go.Scatter(
            x=display_data['strike'],
            y=display_data['call_delta'],
            mode='lines+markers',
            name='📞 Call Delta',
            line=dict(color='green', width=3),
            marker=dict(size=8, symbol='circle')
        ))

        fig_greeks.add_trace(go.Scatter(
            x=display_data['strike'],
            y=display_data['put_delta'],
            mode='lines+markers',
            name='📉 Put Delta',
            line=dict(color='red', width=3),
            marker=dict(size=8, symbol='circle')
        ))

        # Add IV as secondary y-axis
        fig_greeks.add_trace(go.Scatter(
            x=display_data['strike'],
            y=display_data['call_iv'],
            mode='lines',
            name='📈 Call IV',
            line=dict(color='cyan', width=2, dash='dot'),
            yaxis='y2'
        ))

        fig_greeks.update_layout(
            title=f"🎯 {symbol} Greeks & IV Profile",
            xaxis_title="Strike Price ($)",
            yaxis_title="Delta",
            yaxis2=dict(title="Implied Volatility", overlaying='y', side='right'),
            template="plotly_dark",
            height=500
        )

        fig_greeks.add_vline(x=current_price, line_dash="dash", line_color="yellow")

        # Summary metrics
        total_call_volume = display_data['call_volume'].sum()
        total_put_volume = display_data['put_volume'].sum()
        put_call_ratio = total_put_volume / total_call_volume if total_call_volume > 0 else 0

        data_source = "🔴 DEMO DATA" if not analyzer.real_data else "🟢 LIVE DATA"

        summary = f"""
        ## 🎯 {symbol} Options Flow Analysis

        **{data_source}** | **Current Price:** ${current_price:.2f}

        ### 📊 Key Metrics
        - **ATM IV:** {atm_iv:.1%}
        - **Expected Move (30d):** ±${expected_move:.2f} ({expected_move/current_price:.1%})
        - **Support/Resistance:** ${current_price - expected_move:.2f} - ${current_price + expected_move:.2f}

        ### 📈 Volume Analysis
        - **Total Call Volume:** {total_call_volume:,}
        - **Total Put Volume:** {total_put_volume:,}
        - **Put/Call Ratio:** {put_call_ratio:.2f} {'🐻 Bearish' if put_call_ratio > 1 else '🐂 Bullish'}

        ### 🔥 Activity Status
        - **Unusual Contracts:** {len(unusual)} detected
        - **Analysis Threshold:** {volume_threshold}σ above normal
        """

        # Prepare table data
        if show_unusual_only and len(unusual) > 0:
            table_data = unusual[['strike', 'activity_type', 'call_volume', 'put_volume',
                                'call_delta', 'put_delta', 'call_iv', 'put_iv']].round(3)
        else:
            table_data = chain[['strike', 'call_volume', 'put_volume', 'call_delta',
                              'put_delta', 'call_iv', 'put_iv']].round(3)

        return summary, fig_volume, fig_greeks, table_data

    except Exception as e:
        error_msg = f"❌ Error analyzing {symbol}: {str(e)}"
        empty_fig = go.Figure().add_annotation(
            text="No data available",
            xref="paper", yref="paper", x=0.5, y=0.5,
            font=dict(size=20, color="white")
        )
        empty_fig.update_layout(template="plotly_dark", height=400)
        return error_msg, empty_fig, empty_fig, pd.DataFrame()

# Create the Gradio interface
with gr.Blocks(
    theme=gr.themes.Soft(primary_hue="blue").set(
        body_background_fill="*neutral_950",
        block_background_fill="*neutral_900",
        input_background_fill="*neutral_800"
    ),
    title="Options Flow Analyzer"
) as app:

    gr.Markdown("""
    # 🎯 Advanced Options Flow Analyzer
    *Real-time options analysis with anomaly detection • Inspired by Polygon.io examples*
    """)

    with gr.Row():
        with gr.Column(scale=2):
            symbol_input = gr.Textbox(
                label="📈 Stock Symbol",
                value="AAPL",
                placeholder="Enter symbol (AAPL, TSLA, NVDA, etc.)",
                info="Enter any valid stock ticker symbol"
            )

        with gr.Column(scale=1):
            unusual_only = gr.Checkbox(
                label="🔥 Show Unusual Activity Only",
                value=False,
                info="Filter for high-volume anomalies"
            )

        with gr.Column(scale=1):
            threshold_slider = gr.Slider(
                minimum=1.0, maximum=4.0, value=2.0, step=0.1,
                label="🎚️ Anomaly Threshold (σ)",
                info="Higher = more selective"
            )

    analyze_btn = gr.Button("🚀 Analyze Options Flow", variant="primary", size="lg")

    with gr.Row():
        summary_output = gr.Markdown()

    with gr.Row():
        with gr.Column():
            volume_chart = gr.Plot(label="📊 Volume Analysis")
        with gr.Column():
            greeks_chart = gr.Plot(label="🎯 Greeks & IV Profile")

    options_table = gr.Dataframe(
        label="📋 Options Chain Data",
        wrap=True
    )

    # Event handlers
    analyze_btn.click(
        analyze_options_comprehensive,
        inputs=[symbol_input, unusual_only, threshold_slider],
        outputs=[summary_output, volume_chart, greeks_chart, options_table]
    )

    # Auto-analyze on symbol change
    symbol_input.submit(
        analyze_options_comprehensive,
        inputs=[symbol_input, unusual_only, threshold_slider],
        outputs=[summary_output, volume_chart, greeks_chart, options_table]
    )

    gr.Markdown(f"""
    ---
    ### 📝 About This Tool

    **Data Source:** {'🟢 Polygon.io API (Live Data)' if analyzer.real_data else '🔴 Demo Mode (Simulated Data)'}

    **Features:**
    - 🎯 Real-time options chain analysis
    - 🔥 Statistical anomaly detection (Z-score based)
    - 📊 Interactive volume and Greeks visualization
    - 🎚️ Adjustable sensitivity thresholds
    - 🌙 Dark theme optimized for trading

    **To Enable Live Data:** Set your `POLYGON_API_KEY` environment variable

    *Inspired by the official Polygon.io Python client examples*
    """)

if __name__ == "__main__":
    import socket

    def find_free_port():
        """Find a free port to use"""
        for port in range(7860, 7870):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        return 7870  # fallback

    free_port = find_free_port()
    print(f"🚀 Starting server on port {free_port}")

    app.launch(
        share=True,
        server_name="0.0.0.0",
        server_port=free_port,
        show_error=True
    )
