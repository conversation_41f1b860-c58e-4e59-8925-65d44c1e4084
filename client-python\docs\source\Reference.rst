.. _reference_header:

Reference
===============

====================
Get market holidays
====================

- `Stocks market holidays`_
- `Options market holidays`_
- `Forex market holidays`_
- `Crypto market holidays`_

.. automethod:: polygon.RESTClient.get_market_holidays

====================
Get market status
====================

- `Stocks market status`_
- `Options market status`_
- `Forex market status`_
- `Crypto market status`_

.. automethod:: polygon.RESTClient.get_market_status

====================
List tickers
====================

- `Stocks tickers`_
- `Options tickers`_
- `Forex tickers`_
- `Crypto tickers`_

.. automethod:: polygon.RESTClient.list_tickers

====================
Get ticker details
====================

- `Stocks ticker details`_
- `Options ticker details`_

.. automethod:: polygon.RESTClient.get_ticker_details

====================
List ticker news
====================

- `Stocks ticker news`_
- `Options ticker news`_

.. automethod:: polygon.RESTClient.list_ticker_news

====================
Get ticker types
====================

- `Stocks ticker types`_
- `Options ticker types`_

.. automethod:: polygon.RESTClient.get_ticker_types

====================
List splits
====================

- `Stocks splits`_

.. automethod:: polygon.RESTClient.list_splits

====================
List dividends
====================

- `Stocks dividends`_

.. automethod:: polygon.RESTClient.list_dividends

====================
List conditions
====================

- `Stocks conditions`_
- `Options conditions`_
- `Forex conditions`_
- `Crypto conditions`_

.. automethod:: polygon.RESTClient.list_conditions

====================
Get exchanges
====================

- `Stocks exchanges`_
- `Options exchanges`_
- `Forex exchanges`_
- `Crypto exchanges`_

.. automethod:: polygon.RESTClient.get_exchanges

.. _Stocks market holidays: https://polygon.io/docs/stocks/get_v1_marketstatus_upcoming
.. _Options market holidays: https://polygon.io/docs/options/get_v1_marketstatus_upcoming
.. _Forex market holidays: https://polygon.io/docs/forex/get_v1_marketstatus_upcoming
.. _Crypto market holidays: https://polygon.io/docs/crypto/get_v1_marketstatus_upcoming
.. _Stocks market status: https://polygon.io/docs/stocks/get_v1_marketstatus_now
.. _Options market status: https://polygon.io/docs/options/get_v1_marketstatus_now
.. _Forex market status: https://polygon.io/docs/forex/get_v1_marketstatus_now
.. _Crypto market status: https://polygon.io/docs/crypto/get_v1_marketstatus_now
.. _Stocks tickers: https://polygon.io/docs/stocks/get_v3_reference_tickers
.. _Options tickers: https://polygon.io/docs/options/get_v3_reference_tickers
.. _Forex tickers: https://polygon.io/docs/forex/get_v3_reference_tickers
.. _Crypto tickers: https://polygon.io/docs/crypto/get_v3_reference_tickers
.. _Stocks ticker details: https://polygon.io/docs/stocks/get_v3_reference_tickers__ticker
.. _Options ticker details: https://polygon.io/docs/options/get_v3_reference_tickers__ticker
.. _Stocks ticker news: https://polygon.io/docs/stocks/get_v2_reference_news
.. _Options ticker news: https://polygon.io/docs/options/get_v2_reference_news
.. _Stocks ticker types: https://polygon.io/docs/stocks/get_v3_reference_tickers_types
.. _Options ticker types: https://polygon.io/docs/options/get_v3_reference_tickers_types
.. _Stocks splits: https://polygon.io/docs/stocks/get_v3_reference_splits
.. _Stocks dividends: https://polygon.io/docs/stocks/get_v3_reference_dividends
.. _Stocks conditions: https://polygon.io/docs/stocks/get_v3_reference_conditions
.. _Options conditions: https://polygon.io/docs/options/get_v3_reference_conditions
.. _Forex conditions: https://polygon.io/docs/forex/get_v3_reference_conditions
.. _Crypto conditions: https://polygon.io/docs/crypto/get_v3_reference_conditions
.. _Stocks exchanges: https://polygon.io/docs/stocks/get_v3_reference_exchanges
.. _Options exchanges: https://polygon.io/docs/options/get_v3_reference_exchanges
.. _Forex exchanges: https://polygon.io/docs/forex/get_v3_reference_exchanges
.. _Crypto exchanges: https://polygon.io/docs/crypto/get_v3_reference_exchanges