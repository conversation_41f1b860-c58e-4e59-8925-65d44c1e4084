"""
Historical Volatility Calculation Breakdown
Shows exactly how HV is calculated step-by-step with real data
"""

import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def demonstrate_hv_calculation():
    """Show HV calculation with real example"""
    print("📊 Historical Volatility Calculation Breakdown")
    print("=" * 60)
    
    # Sample AAPL prices (last 25 days for 20-day HV)
    sample_data = {
        'date': pd.date_range(start='2024-05-15', periods=25, freq='D'),
        'close': [
            189.50, 191.20, 189.80, 192.35, 194.12,  # Days 1-5
            193.45, 195.67, 197.23, 194.88, 196.45,  # Days 6-10
            198.12, 195.34, 193.67, 191.23, 189.45,  # Days 11-15
            187.89, 185.23, 183.45, 186.78, 189.34,  # Days 16-20
            192.67, 195.45, 198.23, 196.78, 194.35   # Days 21-25
        ]
    }
    
    df = pd.DataFrame(sample_data)
    df.set_index('date', inplace=True)
    
    print("📈 Sample AAPL Price Data (Last 25 Days):")
    print(df.tail(10))
    
    print(f"\n🔢 Step-by-Step HV Calculation:")
    print("-" * 40)
    
    # Step 1: Calculate daily log returns
    print("Step 1: Daily Log Returns")
    df['daily_return'] = np.log(df['close'] / df['close'].shift(1))
    
    print("Formula: ln(Price_today / Price_yesterday)")
    print("Sample calculations:")
    for i in range(1, 6):
        price_today = df['close'].iloc[i]
        price_yesterday = df['close'].iloc[i-1]
        log_return = df['daily_return'].iloc[i]
        print(f"  Day {i+1}: ln({price_today:.2f} / {price_yesterday:.2f}) = {log_return:.6f}")
    
    # Step 2: Rolling standard deviation
    print(f"\nStep 2: Rolling Standard Deviation (20-day window)")
    df['rolling_std'] = df['daily_return'].rolling(window=20, min_periods=20).std()
    
    latest_returns = df['daily_return'].dropna().tail(20)
    manual_std = latest_returns.std()
    
    print(f"Last 20 daily returns:")
    print(f"  {latest_returns.values}")
    print(f"Standard deviation: {manual_std:.6f}")
    
    # Step 3: Annualization
    print(f"\nStep 3: Annualization")
    annual_factor = np.sqrt(252)
    print(f"Annual factor (√252): {annual_factor:.4f}")
    
    df['hv_decimal'] = df['rolling_std'] * annual_factor
    df['hv_percent'] = df['hv_decimal'] * 100
    
    latest_hv = df['hv_percent'].iloc[-1]
    print(f"Annualized volatility: {manual_std:.6f} × {annual_factor:.4f} = {manual_std * annual_factor:.6f}")
    print(f"As percentage: {manual_std * annual_factor * 100:.2f}%")
    
    # Step 4: Final result
    print(f"\nStep 4: Final Historical Volatility")
    print(f"20-day HV: {latest_hv:.2f}%")
    
    # Show the mathematical meaning
    print(f"\n💡 What This Means:")
    print(f"• AAPL's price has been moving with {latest_hv:.1f}% annual volatility")
    print(f"• Daily volatility: {latest_hv/annual_factor:.2f}%")
    print(f"• Expected daily move: ±{df['close'].iloc[-1] * (latest_hv/100/annual_factor):.2f}")
    
    # Compare different time windows
    print(f"\n📊 HV for Different Time Windows:")
    for window in [10, 20, 30, 60]:
        if len(df) >= window:
            hv_window = df['daily_return'].rolling(window=window).std().iloc[-1] * np.sqrt(252) * 100
            print(f"  {window:2d}-day HV: {hv_window:.2f}%")
    
    return df

def show_hv_vs_simple_volatility():
    """Show why we use log returns vs simple returns"""
    print(f"\n🔬 Why Log Returns vs Simple Returns?")
    print("=" * 50)
    
    # Example: Stock goes from $100 to $110 to $100
    prices = [100, 110, 100]
    
    print("Example: Stock price $100 → $110 → $100")
    
    # Simple returns
    simple_returns = []
    for i in range(1, len(prices)):
        simple_ret = (prices[i] - prices[i-1]) / prices[i-1]
        simple_returns.append(simple_ret)
    
    print(f"\nSimple returns:")
    print(f"  Day 1→2: ({prices[1]} - {prices[0]}) / {prices[0]} = {simple_returns[0]:.3f} (+10.0%)")
    print(f"  Day 2→3: ({prices[2]} - {prices[1]}) / {prices[1]} = {simple_returns[1]:.3f} (-9.1%)")
    print(f"  Sum: {sum(simple_returns):.3f} (≠ 0, even though price returned to start)")
    
    # Log returns
    log_returns = []
    for i in range(1, len(prices)):
        log_ret = np.log(prices[i] / prices[i-1])
        log_returns.append(log_ret)
    
    print(f"\nLog returns:")
    print(f"  Day 1→2: ln({prices[1]} / {prices[0]}) = {log_returns[0]:.6f}")
    print(f"  Day 2→3: ln({prices[2]} / {prices[1]}) = {log_returns[1]:.6f}")
    print(f"  Sum: {sum(log_returns):.6f} (≈ 0, correctly shows no net change)")
    
    print(f"\n✅ Log returns are symmetric and additive - that's why we use them!")

def main():
    """Main demonstration"""
    df = demonstrate_hv_calculation()
    show_hv_vs_simple_volatility()
    
    print(f"\n📚 Key Takeaways:")
    print("1. HV uses logarithmic returns (industry standard)")
    print("2. Rolling window captures recent volatility patterns")
    print("3. √252 annualization accounts for trading calendar")
    print("4. Result shows expected annual price movement range")
    print("5. Higher HV = more volatile stock = higher option premiums")

if __name__ == "__main__":
    main()
