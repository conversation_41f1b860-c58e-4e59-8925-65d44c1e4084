"""
Historical Volatility vs Implied Volatility Analyzer
Real-time comparison using Polygon.io API data

Creates professional-grade HV vs IV charts for options trading analysis
"""

import os
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

try:
    from polygon import RESTClient
    POLYGON_AVAILABLE = True
except ImportError:
    POLYGON_AVAILABLE = False
    print("Install polygon-api-client: pip install polygon-api-client")

class HVvsIVAnalyzer:
    def __init__(self):
        self.api_key = os.getenv('POLYGON_API_KEY')
        if self.api_key and POLYGON_AVAILABLE:
            self.client = RESTClient(self.api_key)
            self.real_data = True
            print("✅ Connected to Polygon.io API for real HV/IV data")
        else:
            self.client = None
            self.real_data = False
            print("⚠️ Using demo data. Set POLYGON_API_KEY for real data.")
    
    def get_historical_prices(self, symbol, days=180):
        """Get historical stock prices for HV calculation"""
        if self.real_data:
            try:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)
                
                print(f"📊 Fetching {days} days of price data for {symbol}...")
                
                aggs = []
                for agg in self.client.list_aggs(
                    symbol,
                    1,
                    "day",
                    start_date.strftime("%Y-%m-%d"),
                    end_date.strftime("%Y-%m-%d"),
                    limit=50000
                ):
                    aggs.append({
                        'date': datetime.fromtimestamp(agg.timestamp / 1000),
                        'close': agg.close,
                        'open': agg.open,
                        'high': agg.high,
                        'low': agg.low,
                        'volume': agg.volume
                    })
                
                if aggs:
                    df = pd.DataFrame(aggs)
                    df.set_index('date', inplace=True)
                    df.sort_index(inplace=True)
                    print(f"✅ Retrieved {len(df)} days of real price data")
                    return df
                
            except Exception as e:
                print(f"⚠️ Error fetching real data: {e}")
        
        # Fallback to mock data
        return self._generate_mock_prices(symbol, days)
    
    def _generate_mock_prices(self, symbol, days):
        """Generate realistic mock price data"""
        print(f"📊 Generating {days} days of demo data for {symbol}...")
        
        np.random.seed(hash(symbol) % 1000)
        
        # Base prices for different symbols
        base_prices = {
            'SPY': 450, 'AAPL': 180, 'MSFT': 350, 'GOOGL': 140,
            'TSLA': 200, 'NVDA': 450, 'AMZN': 150, 'META': 300
        }
        
        start_price = base_prices.get(symbol, 100)
        dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
        
        # Generate realistic price movements with volatility clustering
        prices = [start_price]
        volatility = 0.02  # Base 2% daily volatility
        
        for i in range(days - 1):
            # Add volatility clustering
            if i % 30 < 10:  # High vol periods
                vol = volatility * np.random.uniform(1.5, 2.5)
            else:
                vol = volatility * np.random.uniform(0.7, 1.3)
            
            # Generate return with mean reversion
            drift = 0.0005  # Slight upward drift
            shock = np.random.normal(drift, vol)
            new_price = prices[-1] * (1 + shock)
            prices.append(max(new_price, start_price * 0.5))  # Floor price
        
        df = pd.DataFrame({
            'close': prices,
            'open': [p * np.random.uniform(0.995, 1.005) for p in prices],
            'high': [p * np.random.uniform(1.0, 1.02) for p in prices],
            'low': [p * np.random.uniform(0.98, 1.0) for p in prices],
            'volume': [np.random.randint(1000000, 10000000) for _ in prices]
        }, index=dates)
        
        print(f"✅ Generated {len(df)} days of demo data")
        return df
    
    def calculate_historical_volatility(self, prices, window=20):
        """Calculate rolling historical volatility"""
        # Daily log returns
        log_returns = np.log(prices / prices.shift(1))
        
        # Rolling standard deviation
        rolling_std = log_returns.rolling(window=window).std()
        
        # Annualize (252 trading days)
        hv = rolling_std * np.sqrt(252) * 100
        
        return hv.dropna()
    
    def get_atm_implied_volatility(self, symbol, prices):
        """Get ATM implied volatility for ~20 DTE options"""
        if self.real_data:
            try:
                return self._get_real_iv(symbol, prices)
            except Exception as e:
                print(f"⚠️ Error fetching real IV: {e}")
        
        # Generate realistic mock IV data
        return self._generate_mock_iv(symbol, prices)
    
    def _get_real_iv(self, symbol, prices):
        """Fetch real implied volatility from Polygon.io"""
        print(f"📈 Fetching real options IV data for {symbol}...")
        
        iv_data = []
        current_price = prices['close'].iloc[-1]
        
        # Get options chain for analysis
        try:
            target_date = (datetime.now() + timedelta(days=20)).strftime("%Y-%m-%d")
            
            options = []
            for option in self.client.list_snapshot_options_chain(
                symbol,
                params={
                    "expiration_date.gte": target_date,
                    "expiration_date.lte": (datetime.now() + timedelta(days=25)).strftime("%Y-%m-%d"),
                    "limit": 50
                }
            ):
                if hasattr(option, 'details') and hasattr(option, 'implied_volatility'):
                    options.append(option)
            
            if options:
                # Find ATM option (closest to current price)
                atm_option = min(options, 
                    key=lambda x: abs(x.details.strike_price - current_price))
                
                if hasattr(atm_option, 'implied_volatility') and atm_option.implied_volatility:
                    iv_value = atm_option.implied_volatility * 100  # Convert to percentage
                    
                    # Create IV series matching price dates
                    iv_series = pd.Series(
                        [iv_value] * len(prices), 
                        index=prices.index
                    )
                    
                    print(f"✅ Real ATM IV: {iv_value:.2f}%")
                    return iv_series
            
        except Exception as e:
            print(f"Error in real IV fetch: {e}")
        
        # Fallback to mock data
        return self._generate_mock_iv(symbol, prices)
    
    def _generate_mock_iv(self, symbol, prices):
        """Generate realistic mock implied volatility"""
        print(f"📈 Generating demo IV data for {symbol}...")
        
        # Calculate HV as base
        hv = self.calculate_historical_volatility(prices['close'], 20)
        
        # IV typically trades at premium/discount to HV
        np.random.seed(hash(symbol + "iv") % 1000)
        
        iv_data = []
        for i, (date, hv_val) in enumerate(hv.items()):
            # IV/HV ratio varies between 0.8 and 1.4
            if i < len(hv) * 0.3:  # Early period - IV > HV
                ratio = np.random.uniform(1.1, 1.4)
            elif i < len(hv) * 0.7:  # Middle period - IV ≈ HV
                ratio = np.random.uniform(0.9, 1.1)
            else:  # Recent period - IV < HV
                ratio = np.random.uniform(0.8, 1.0)
            
            # Add some noise and mean reversion
            noise = np.random.normal(0, 0.02)
            iv_val = hv_val * ratio + noise
            iv_data.append(max(iv_val, 5))  # Floor at 5%
        
        iv_series = pd.Series(iv_data, index=hv.index)
        print(f"✅ Generated IV data with realistic HV/IV relationship")
        return iv_series
    
    def create_hv_iv_chart(self, symbol):
        """Create the main HV vs IV comparison chart"""
        print(f"\n🎯 Creating HV vs IV Chart for {symbol}")
        print("=" * 50)
        
        # Get historical price data
        prices = self.get_historical_prices(symbol, days=180)
        
        if len(prices) < 30:
            raise ValueError("Insufficient price data")
        
        # Calculate 20-day Historical Volatility
        hv = self.calculate_historical_volatility(prices['close'], window=20)
        
        # Get ATM Implied Volatility (~20 DTE)
        iv = self.get_atm_implied_volatility(symbol, prices)
        
        # Align data (IV might be shorter)
        common_dates = hv.index.intersection(iv.index)
        hv_aligned = hv.loc[common_dates]
        iv_aligned = iv.loc[common_dates]
        
        # Create the chart
        fig = go.Figure()
        
        # Historical Volatility line
        fig.add_trace(go.Scatter(
            x=hv_aligned.index,
            y=hv_aligned.values,
            mode='lines',
            name='20-Day Historical Volatility',
            line=dict(color='blue', width=2),
            hovertemplate='<b>HV:</b> %{y:.2f}%<br><b>Date:</b> %{x}<extra></extra>'
        ))
        
        # Implied Volatility line
        fig.add_trace(go.Scatter(
            x=iv_aligned.index,
            y=iv_aligned.values,
            mode='lines',
            name='ATM Implied Volatility (20 DTE Equivalent)',
            line=dict(color='red', width=2),
            hovertemplate='<b>IV:</b> %{y:.2f}%<br><b>Date:</b> %{x}<extra></extra>'
        ))
        
        # Add shaded regions for IV > HV and IV < HV
        fig.add_trace(go.Scatter(
            x=hv_aligned.index.tolist() + hv_aligned.index.tolist()[::-1],
            y=iv_aligned.values.tolist() + hv_aligned.values.tolist()[::-1],
            fill='tonexty',
            fillcolor='rgba(255, 0, 0, 0.1)',
            line=dict(color='rgba(255,255,255,0)'),
            name='IV > HV (Options Expensive)',
            showlegend=True,
            hoverinfo='skip'
        ))
        
        # Calculate current metrics
        current_hv = hv_aligned.iloc[-1]
        current_iv = iv_aligned.iloc[-1]
        iv_hv_ratio = current_iv / current_hv
        
        # Update layout
        data_source = "Real Polygon.io Data" if self.real_data else "Demo Data"
        
        fig.update_layout(
            title=f'20-Day Historical vs. ATM Implied Volatility - {symbol}<br><sub>Data Source: {data_source} | Current IV/HV Ratio: {iv_hv_ratio:.2f}</sub>',
            xaxis_title='Date',
            yaxis_title='Volatility Percentage (%)',
            template='plotly_dark',
            height=600,
            hovermode='x unified',
            legend=dict(
                yanchor="top",
                y=0.99,
                xanchor="left",
                x=0.01
            )
        )
        
        # Add annotations for current values
        fig.add_annotation(
            x=hv_aligned.index[-1],
            y=current_hv,
            text=f"HV: {current_hv:.1f}%",
            showarrow=True,
            arrowhead=2,
            arrowcolor="blue",
            bgcolor="blue",
            bordercolor="white",
            font=dict(color="white")
        )
        
        fig.add_annotation(
            x=iv_aligned.index[-1],
            y=current_iv,
            text=f"IV: {current_iv:.1f}%",
            showarrow=True,
            arrowhead=2,
            arrowcolor="red",
            bgcolor="red",
            bordercolor="white",
            font=dict(color="white")
        )
        
        # Generate trading signal
        if iv_hv_ratio > 1.2:
            signal = "🔴 SELL OPTIONS (IV > HV)"
            signal_color = "red"
        elif iv_hv_ratio < 0.8:
            signal = "🟢 BUY OPTIONS (IV < HV)"
            signal_color = "green"
        else:
            signal = "🟡 NEUTRAL (IV ≈ HV)"
            signal_color = "yellow"
        
        # Summary statistics
        summary = f"""
        ## 📊 {symbol} Volatility Analysis Summary
        
        **Current Metrics:**
        - Historical Volatility (20d): {current_hv:.2f}%
        - Implied Volatility (ATM): {current_iv:.2f}%
        - IV/HV Ratio: {iv_hv_ratio:.2f}
        
        **Trading Signal:** {signal}
        
        **Analysis Period:** {len(hv_aligned)} days
        **Data Source:** {data_source}
        
        **Interpretation:**
        - IV > HV: Options may be expensive (consider selling)
        - IV < HV: Options may be cheap (consider buying)
        - IV ≈ HV: Options fairly valued
        """
        
        return fig, summary
    
    def analyze_multiple_symbols(self, symbols):
        """Analyze multiple symbols for comparison"""
        results = {}
        
        for symbol in symbols:
            try:
                prices = self.get_historical_prices(symbol, days=60)
                hv = self.calculate_historical_volatility(prices['close'], 20)
                iv = self.get_atm_implied_volatility(symbol, prices)
                
                current_hv = hv.iloc[-1]
                current_iv = iv.iloc[-1] if len(iv) > 0 else current_hv * 1.1
                
                results[symbol] = {
                    'HV_20d': current_hv,
                    'IV_ATM': current_iv,
                    'IV_HV_Ratio': current_iv / current_hv,
                    'Signal': 'SELL' if current_iv / current_hv > 1.2 else 'BUY' if current_iv / current_hv < 0.8 else 'NEUTRAL'
                }
                
            except Exception as e:
                print(f"Error analyzing {symbol}: {e}")
        
        return pd.DataFrame(results).T

def main():
    print("📊 HV vs IV Professional Options Analysis")
    print("=" * 50)
    
    analyzer = HVvsIVAnalyzer()
    
    # Single symbol analysis
    symbol = "AAPL"  # Change this to any symbol
    
    try:
        fig, summary = analyzer.create_hv_iv_chart(symbol)
        
        print(summary)
        
        # Show the chart
        fig.show()
        
        # Multi-symbol comparison
        symbols = ["AAPL", "SPY", "TSLA", "NVDA"]
        print(f"\n📈 Multi-Symbol Volatility Comparison:")
        comparison = analyzer.analyze_multiple_symbols(symbols)
        print(comparison.round(2))
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
