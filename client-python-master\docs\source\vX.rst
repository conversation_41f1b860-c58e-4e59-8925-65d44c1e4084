.. _vX_header:

vX
==========

.. note::
    To call vX methods, use the vx class member on RESTClient.

    For example, :code:`financials = RESTClient().vx.list_stock_financials()`

======================
List stock financials
======================

- `Stocks financials vX`_

.. automethod:: polygon.rest.VXClient.list_stock_financials

.. _Stocks financials vX: https://polygon.io/docs/stocks/get_vx_reference_financials
