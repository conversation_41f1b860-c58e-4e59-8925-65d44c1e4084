"""
Interactive HV vs IV Chart with Gradio
Clean UI with proper loading indicators and the design you liked
"""

import gradio as gr
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Import our data engine
from production_hv_iv_analyzer import ProductionHVIVAnalyzer

class GradioHVIVChart:
    def __init__(self):
        self.analyzer = ProductionHVIVAnalyzer(cache_enabled=True)

        # Time period options
        self.time_periods = {
            "3 Months": 90,
            "6 Months": 180,
            "1 Year": 365,
            "2 Years": 730,
            "4 Years": 1460
        }

    def create_chart_with_loading(self, symbol, period_name, progress=gr.Progress()):
        """Create chart with proper loading indicators"""
        try:
            if not symbol or not symbol.strip():
                return None, "Please enter a valid symbol"

            symbol = symbol.upper().strip()
            period_days = self.time_periods[period_name]

            # Progress tracking
            progress(0.1, desc="Initializing...")

            # Get price data
            progress(0.2, desc=f"Fetching {period_days} days of price data...")
            prices = self.analyzer.get_stock_prices(symbol, days=period_days)

            # Calculate HV
            progress(0.4, desc="Calculating historical volatility...")
            hv = self.analyzer.calculate_historical_volatility(prices['close'], window=20)

            # Get IV data
            progress(0.6, desc="Processing options data for implied volatility...")
            iv_start = prices.index.min()
            iv_end = prices.index.max()
            iv = self.analyzer.get_historical_iv(symbol, iv_start, iv_end)

            # Align data
            progress(0.8, desc="Aligning data and creating chart...")
            common_dates = hv.index.intersection(iv.index)
            if len(common_dates) == 0:
                return None, f"No overlapping data found for {symbol}"

            hv_aligned = hv.loc[common_dates]
            iv_aligned = iv.loc[common_dates]

            # Create the chart
            progress(0.9, desc="Rendering chart...")
            fig = self.create_professional_chart(symbol, period_name, hv_aligned, iv_aligned)

            # Generate summary
            current_hv = hv_aligned.iloc[-1]
            current_iv = iv_aligned.iloc[-1]
            iv_hv_ratio = current_iv / current_hv

            # Trading signal
            if iv_hv_ratio > 1.25:
                signal = "🔴 SELL OPTIONS"
                signal_strength = "STRONG" if iv_hv_ratio > 1.5 else "MODERATE"
            elif iv_hv_ratio < 0.8:
                signal = "🟢 BUY OPTIONS"
                signal_strength = "STRONG" if iv_hv_ratio < 0.65 else "MODERATE"
            else:
                signal = "🟡 NEUTRAL"
                signal_strength = "HOLD"

            summary = f"""
            ## 📊 {symbol} Volatility Analysis - {period_name}

            **Current Metrics:**
            - **Historical Volatility (20d):** {current_hv:.2f}%
            - **Implied Volatility (ATM):** {current_iv:.2f}%
            - **IV/HV Ratio:** {iv_hv_ratio:.2f}

            **Trading Signal:** {signal} ({signal_strength})

            **Data Quality:**
            - **Data Points:** {len(hv_aligned)} days
            - **Date Range:** {hv_aligned.index.min().strftime('%Y-%m-%d')} to {hv_aligned.index.max().strftime('%Y-%m-%d')}
            - **Source:** Real Polygon.io market data
            """

            progress(1.0, desc="Complete!")
            return fig, summary

        except Exception as e:
            error_msg = f"❌ Error analyzing {symbol}: {str(e)}"
            empty_fig = go.Figure()
            empty_fig.add_annotation(
                text=f"Error loading data for {symbol}",
                xref="paper", yref="paper", x=0.5, y=0.5,
                showarrow=False, font=dict(size=16, color="white")
            )
            empty_fig.update_layout(template="plotly_dark", height=600)
            return empty_fig, error_msg

    def create_professional_chart(self, symbol, period_name, hv, iv):
        """Create the chart matching your screenshot style"""
        fig = go.Figure()

        # Historical Volatility line (blue)
        fig.add_trace(go.Scatter(
            x=hv.index,
            y=hv.values,
            mode='lines',
            name='20-Day Historical Volatility',
            line=dict(color='#1f77b4', width=2),
            hovertemplate='<b>HV:</b> %{y:.2f}%<br><b>Date:</b> %{x}<extra></extra>'
        ))

        # Implied Volatility line (red)
        fig.add_trace(go.Scatter(
            x=iv.index,
            y=iv.values,
            mode='lines',
            name='ATM Implied Volatility (20 DTE)',
            line=dict(color='#d62728', width=2),
            hovertemplate='<b>IV:</b> %{y:.2f}%<br><b>Date:</b> %{x}<extra></extra>'
        ))

        # Current value annotations (like in your screenshot)
        current_hv = hv.iloc[-1]
        current_iv = iv.iloc[-1]

        fig.add_annotation(
            x=hv.index[-1], y=current_hv,
            text=f"HV: {current_hv:.1f}%",
            showarrow=True, arrowhead=2, arrowcolor="#1f77b4",
            bgcolor="#1f77b4", bordercolor="white", font=dict(color="white"),
            ax=20, ay=-30
        )

        fig.add_annotation(
            x=iv.index[-1], y=current_iv,
            text=f"IV: {current_iv:.1f}%",
            showarrow=True, arrowhead=2, arrowcolor="#d62728",
            bgcolor="#d62728", bordercolor="white", font=dict(color="white"),
            ax=20, ay=30
        )

        # Style to match your screenshot
        fig.update_layout(
            title=f'{symbol} - Historical vs Implied Volatility Analysis<br><sub>Real Market Data • {period_name} View</sub>',
            xaxis_title='Date',
            yaxis_title='Volatility (%)',
            template='plotly_dark',
            height=600,
            hovermode='x unified',
            legend=dict(
                yanchor="top", y=0.99,
                xanchor="left", x=0.01,
                bgcolor="rgba(0,0,0,0.5)"
            ),
            plot_bgcolor='rgba(0,0,0,0.8)',
            paper_bgcolor='rgba(0,0,0,0.9)'
        )

        return fig

def create_gradio_interface():
    """Create the Gradio interface matching your preferred style"""
    chart_app = GradioHVIVChart()

    # Create the interface with dark theme (like your screenshot)
    with gr.Blocks(
        theme=gr.themes.Soft(primary_hue="blue").set(
            body_background_fill="*neutral_950",
            block_background_fill="*neutral_900",
            input_background_fill="*neutral_800"
        ),
        title="HV vs IV Interactive Chart"
    ) as app:

        gr.Markdown("""
        # 📊 Interactive Historical vs Implied Volatility Chart
        *Professional volatility analysis with dynamic time period selection*
        """)

        with gr.Row():
            with gr.Column(scale=1):
                symbol_input = gr.Textbox(
                    label="📈 Stock Symbol",
                    value="AAPL",
                    placeholder="Enter symbol (AAPL, SPY, TSLA, etc.)",
                    info="Enter any valid stock ticker symbol"
                )

                period_dropdown = gr.Dropdown(
                    label="📅 Time Period",
                    choices=list(chart_app.time_periods.keys()),
                    value="6 Months",
                    info="Select historical analysis period"
                )

                analyze_btn = gr.Button(
                    "🚀 Generate Chart",
                    variant="primary",
                    size="lg"
                )

            with gr.Column(scale=2):
                summary_output = gr.Markdown()

        # Chart output
        chart_output = gr.Plot(
            label="📊 HV vs IV Chart"
        )

        # Event handlers with loading
        analyze_btn.click(
            fn=chart_app.create_chart_with_loading,
            inputs=[symbol_input, period_dropdown],
            outputs=[chart_output, summary_output],
            show_progress=True  # This shows the loading bar
        )

        # Auto-update on Enter
        symbol_input.submit(
            fn=chart_app.create_chart_with_loading,
            inputs=[symbol_input, period_dropdown],
            outputs=[chart_output, summary_output],
            show_progress=True
        )

        # Auto-update on dropdown change
        period_dropdown.change(
            fn=chart_app.create_chart_with_loading,
            inputs=[symbol_input, period_dropdown],
            outputs=[chart_output, summary_output],
            show_progress=True
        )

        gr.Markdown("""
        ---
        **Features:**
        - ✅ Real-time loading indicators
        - ✅ Dynamic time period selection
        - ✅ Professional dark theme
        - ✅ Real market data from Polygon.io
        - ✅ Interactive charts with annotations
        """)

    return app

def main():
    """Launch the Gradio app"""
    print("🚀 Launching Interactive HV vs IV Chart (Gradio)")
    print("=" * 50)

    app = create_gradio_interface()

    print("📊 Features:")
    print("   • Proper loading indicators")
    print("   • Clean dark theme UI")
    print("   • Dynamic time period selection")
    print("   • Real market data processing")
    print("   • Professional chart styling")

    app.launch(
        share=True,
        server_name="0.0.0.0",
        server_port=7865,  # Use specific port
        show_error=True,
        inbrowser=True
    )

if __name__ == "__main__":
    main()
