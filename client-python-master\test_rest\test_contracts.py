from polygon.rest.models import OptionsContract
from base import BaseTest


class ContractsTest(BaseTest):
    def test_get_options_contract(self):
        contract = self.c.get_options_contract("OEVRI240119C00002500")
        expected = OptionsContract(
            additional_underlyings=None,
            cfi="OCASPS",
            contract_type="call",
            correction=None,
            exercise_style="american",
            expiration_date="2024-01-19",
            primary_exchange="BATO",
            shares_per_contract=100,
            strike_price=2.5,
            size=None,
            ticker="O:EVRI240119C00002500",
            underlying_ticker="EVRI",
        )
        self.assertEqual(contract, expected)

    def test_list_options_contracts(self):
        contracts = [c for c in self.c.list_options_contracts()]
        expected = [
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=65,
                size=None,
                ticker="O:A220520C00065000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=70,
                size=None,
                ticker="O:A220520C00070000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=75,
                size=None,
                ticker="O:A220520C00075000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=80,
                size=None,
                ticker="O:A220520C00080000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=85,
                size=None,
                ticker="O:A220520C00085000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=90,
                size=None,
                ticker="O:A220520C00090000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=95,
                size=None,
                ticker="O:A220520C00095000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=100,
                size=None,
                ticker="O:A220520C00100000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=105,
                size=None,
                ticker="O:A220520C00105000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=110,
                size=None,
                ticker="O:A220520C00110000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=115,
                size=None,
                ticker="O:A220520C00115000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=120,
                size=None,
                ticker="O:A220520C00120000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=125,
                size=None,
                ticker="O:A220520C00125000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=130,
                size=None,
                ticker="O:A220520C00130000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=135,
                size=None,
                ticker="O:A220520C00135000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=140,
                size=None,
                ticker="O:A220520C00140000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=145,
                size=None,
                ticker="O:A220520C00145000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=150,
                size=None,
                ticker="O:A220520C00150000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=155,
                size=None,
                ticker="O:A220520C00155000",
                underlying_ticker="A",
            ),
            OptionsContract(
                additional_underlyings=None,
                cfi="OCASPS",
                contract_type="call",
                correction=None,
                exercise_style="american",
                expiration_date="2022-05-20",
                primary_exchange="BATO",
                shares_per_contract=100,
                strike_price=160,
                size=None,
                ticker="O:A220520C00160000",
                underlying_ticker="A",
            ),
        ]
        self.assertEqual(contracts, expected)
