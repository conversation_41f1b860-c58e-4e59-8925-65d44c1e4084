/**
 * Rust Hello World - Memory-Safe Systems Language
 * Features: Memory Safety, Performance, Concurrency
 */

#[derive(Debug)]
struct Language {
    name: String,
    category: String,
    year: u16,
}

impl Language {
    fn new(name: &str, category: &str, year: u16) -> Self {
        Language {
            name: name.to_string(),
            category: category.to_string(),
            year,
        }
    }
}

fn main() {
    println!("🦀 Hello from Rust 1.87.0!");
    println!("✨ Perfect for: Systems Programming, WebAssembly, Performance-Critical Code");

    // Demonstrate Rust features
    let languages = vec![
        Language::new("Python", "AI/Data Science", 1991),
        Language::new("JavaScript", "Web", 1995),
        Language::new("TypeScript", "Web", 2012),
        Language::new("Java", "Enterprise", 1995),
        Language::new("Go", "Systems", 2009),
        Language::new("Rust", "Systems", 2010),
        Language::new("C#", "Enterprise", 2000),
        Language::new("Ruby", "Web", 1995),
        Language::new("PHP", "Web", 1995),
    ];

    println!("📚 You have {} languages installed:", languages.len());

    for (i, lang) in languages.iter().enumerate() {
        println!("  {}. {} ({}, {})", i + 1, lang.name, lang.category, lang.year);
    }

    // Iterator and functional programming
    let systems_languages: Vec<&Language> = languages
        .iter()
        .filter(|lang| lang.category == "Systems")
        .collect();

    let systems_names: Vec<&String> = systems_languages
        .iter()
        .map(|lang| &lang.name)
        .collect();

    println!("⚙️ Systems languages: {}", systems_names.join(", "));

    // Memory safety demonstration
    let rust_info = &languages[5]; // Borrowing, not moving
    println!("🔒 {} ensures memory safety without garbage collection!", rust_info.name);
}
