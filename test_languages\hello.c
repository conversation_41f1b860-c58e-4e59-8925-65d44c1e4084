/**
 * C Hello World - Foundation of Systems Programming
 * Features: Low-Level Control, Performance, Operating Systems
 */

#include <stdio.h>
#include <string.h>

typedef struct {
    char name[20];
    char category[20];
    int year;
} Language;

void print_language(Language lang, int index) {
    printf("  %d. %s (%s, %d)\n", index, lang.name, lang.category, lang.year);
}

int main() {
    printf("⚙️ Hello from C (GCC 14.2.0)!\n");
    printf("✨ Perfect for: Operating Systems, Embedded Systems, Performance-Critical Code\n");

    // Demonstrate C features
    Language languages[] = {
        {"Python", "AI/Data Science", 1991},
        {"JavaScript", "Web", 1995},
        {"TypeScript", "Web", 2012},
        {"Java", "Enterprise", 1995},
        {"Go", "Systems", 2009},
        {"Rust", "Systems", 2010},
        {"C#", "Enterprise", 2000},
        {"C/C++", "Systems", 1972},
        {"Ruby", "Web", 1995},
        {"PHP", "Web", 1995}
    };

    int count = sizeof(languages) / sizeof(languages[0]);
    printf("📚 You have %d languages installed:\n", count);

    for (int i = 0; i < count; i++) {
        print_language(languages[i], i + 1);
    }

    // Manual filtering (no built-in filter like modern languages)
    printf("⚙️ Systems languages: ");
    int first = 1;
    for (int i = 0; i < count; i++) {
        if (strcmp(languages[i].category, "Systems") == 0) {
            if (!first) printf(", ");
            printf("%s", languages[i].name);
            first = 0;
        }
    }
    printf("\n");

    printf("🔧 C is the foundation that many other languages are built upon!\n");
    
    return 0;
}
