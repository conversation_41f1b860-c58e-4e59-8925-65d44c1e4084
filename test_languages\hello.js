#!/usr/bin/env node
/**
 * JavaScript Hello World - Universal Web Language
 * Features: Frontend, Backend (Node.js), Full-Stack Development
 */

function main() {
    console.log("🌐 Hello from JavaScript (Node.js v22.14.0)!");
    console.log("✨ Perfect for: Web Development, APIs, Full-Stack Apps");
    
    // Demonstrate modern JavaScript features
    const languages = ["Python", "JavaScript", "TypeScript", "Java", "Go", "Rust", "C#", "C/C++", "Ruby", "PHP"];
    console.log(`📚 You have ${languages.length} languages installed:`);
    
    languages.forEach((lang, index) => {
        console.log(`  ${index + 1}. ${lang}`);
    });
    
    // Arrow functions and array methods
    const webLanguages = languages.filter(lang => 
        ["JavaScript", "TypeScript", "PHP", "Python"].includes(lang)
    );
    console.log(`🌐 Web languages: ${webLanguages.join(", ")}`);
    
    // Async example
    setTimeout(() => {
        console.log("⚡ JavaScript is asynchronous by nature!");
    }, 100);
}

main();
