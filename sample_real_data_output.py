"""
Sample Real Data Output - Show actual format with 10 days of real AAPL data
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from production_hv_iv_analyzer import ProductionHVIVAnalyzer

def fetch_and_save_sample_data():
    """Fetch 10 days of real AAPL data and save in proposed format"""
    print("🔍 FETCHING 10 DAYS OF REAL AAPL DATA")
    print("=" * 50)
    
    try:
        # Initialize analyzer
        analyzer = ProductionHVIVAnalyzer(cache_enabled=False)  # No cache for fresh data
        
        # Create data directory
        os.makedirs("sample_data", exist_ok=True)
        os.makedirs("sample_data/prices", exist_ok=True)
        os.makedirs("sample_data/options_iv", exist_ok=True)
        os.makedirs("sample_data/calculated", exist_ok=True)
        
        print("📊 Fetching 15 days of AAPL price data (to get 10 clean days)...")
        
        # Get price data
        prices = analyzer.get_stock_prices("AAPL", days=15)
        
        # Take only the last 10 days
        sample_prices = prices.tail(10).copy()
        sample_prices['symbol'] = 'AAPL'
        
        print(f"✅ Got {len(sample_prices)} days of price data")
        print("\n📈 SAMPLE PRICE DATA:")
        print(sample_prices.to_string())
        
        # Save price data
        sample_prices.to_parquet("sample_data/prices/AAPL_prices_sample.parquet")
        sample_prices.to_csv("sample_data/prices/AAPL_prices_sample.csv")
        
        print(f"\n💾 Saved price data to sample_data/prices/")
        
        # Calculate HV for these dates
        print(f"\n🧮 Calculating Historical Volatility...")
        
        # Need more data for HV calculation (20-day window)
        extended_prices = analyzer.get_stock_prices("AAPL", days=35)
        hv_full = analyzer.calculate_historical_volatility(extended_prices['close'], window=20)
        
        # Get HV for our sample dates
        sample_dates = sample_prices.index
        hv_sample = hv_full[hv_full.index.isin(sample_dates)].copy()
        
        # Create HV dataframe
        hv_df = pd.DataFrame({
            'date': hv_sample.index,
            'hv_20d': hv_sample.values,
            'symbol': 'AAPL'
        })
        hv_df.set_index('date', inplace=True)
        
        print(f"✅ Calculated HV for {len(hv_df)} days")
        print("\n📊 SAMPLE HV DATA:")
        print(hv_df.to_string())
        
        # Save HV data
        hv_df.to_parquet("sample_data/calculated/AAPL_hv_20d_sample.parquet")
        hv_df.to_csv("sample_data/calculated/AAPL_hv_20d_sample.csv")
        
        print(f"\n💾 Saved HV data to sample_data/calculated/")
        
        # Get IV data for sample dates
        print(f"\n📈 Fetching Historical IV data...")
        
        iv_start = sample_dates.min()
        iv_end = sample_dates.max()
        
        try:
            iv_series = analyzer.get_historical_iv("AAPL", iv_start, iv_end)
            
            # Create IV summary dataframe
            iv_df = pd.DataFrame({
                'date': iv_series.index,
                'atm_iv_20dte': iv_series.values,
                'symbol': 'AAPL'
            })
            iv_df.set_index('date', inplace=True)
            
            print(f"✅ Got IV data for {len(iv_df)} days")
            print("\n📊 SAMPLE IV DATA:")
            print(iv_df.to_string())
            
            # Save IV data
            iv_df.to_parquet("sample_data/calculated/AAPL_atm_iv_20dte_sample.parquet")
            iv_df.to_csv("sample_data/calculated/AAPL_atm_iv_20dte_sample.csv")
            
            print(f"\n💾 Saved IV data to sample_data/calculated/")
            
        except Exception as e:
            print(f"⚠️ IV data fetch failed: {e}")
            print("Creating sample IV data structure...")
            
            # Create sample IV structure
            iv_sample_data = []
            for date in sample_dates:
                iv_sample_data.append({
                    'date': date,
                    'atm_iv_20dte': np.random.uniform(20, 30),  # Sample IV values
                    'symbol': 'AAPL'
                })
            
            iv_df = pd.DataFrame(iv_sample_data)
            iv_df.set_index('date', inplace=True)
            
            print("📊 SAMPLE IV DATA STRUCTURE:")
            print(iv_df.to_string())
            
            iv_df.to_parquet("sample_data/calculated/AAPL_atm_iv_20dte_sample.parquet")
            iv_df.to_csv("sample_data/calculated/AAPL_atm_iv_20dte_sample.csv")
        
        # Create combined analysis file
        print(f"\n🔗 Creating combined analysis file...")
        
        # Merge all data
        combined = sample_prices[['close']].copy()
        combined = combined.join(hv_df[['hv_20d']], how='left')
        combined = combined.join(iv_df[['atm_iv_20dte']], how='left')
        combined['symbol'] = 'AAPL'
        
        print("\n📊 COMBINED SAMPLE DATA (Ready for Chart):")
        print(combined.to_string())
        
        # Save combined data
        combined.to_parquet("sample_data/AAPL_combined_sample.parquet")
        combined.to_csv("sample_data/AAPL_combined_sample.csv")
        
        print(f"\n💾 Saved combined data to sample_data/")
        
        # Show file sizes
        print(f"\n📁 FILE SIZES:")
        for root, dirs, files in os.walk("sample_data"):
            for file in files:
                filepath = os.path.join(root, file)
                size = os.path.getsize(filepath)
                print(f"  {filepath}: {size:,} bytes")
        
        print(f"\n✅ SAMPLE DATA GENERATION COMPLETE!")
        print(f"📂 Check the 'sample_data' folder for all files")
        print(f"🎯 This shows exactly how 500 stocks × 2 years will be stored")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def load_and_display_sample():
    """Load the sample data and show how fast it loads"""
    print(f"\n⚡ TESTING LOAD SPEED...")
    
    try:
        import time
        
        # Test Parquet loading speed
        start_time = time.time()
        df_parquet = pd.read_parquet("sample_data/AAPL_combined_sample.parquet")
        parquet_time = time.time() - start_time
        
        # Test CSV loading speed
        start_time = time.time()
        df_csv = pd.read_csv("sample_data/AAPL_combined_sample.csv", index_col=0, parse_dates=True)
        csv_time = time.time() - start_time
        
        print(f"📊 LOAD SPEED COMPARISON:")
        print(f"  Parquet: {parquet_time:.4f} seconds")
        print(f"  CSV: {csv_time:.4f} seconds")
        print(f"  Speedup: {csv_time/parquet_time:.1f}x faster with Parquet")
        
        print(f"\n📈 LOADED DATA PREVIEW:")
        print(df_parquet.head())
        
    except Exception as e:
        print(f"⚠️ Load test failed: {e}")

if __name__ == "__main__":
    # Fetch and save real data
    fetch_and_save_sample_data()
    
    # Test loading speed
    load_and_display_sample()
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Review the sample_data folder")
    print(f"2. Check file formats and sizes")
    print(f"3. Confirm this structure works for 500 stocks")
    print(f"4. Build the bulk download system")
