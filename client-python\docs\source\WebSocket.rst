.. _websocket_header:

WebSocket
==========

- `Stock<PERSON> getting started`_
- `Options getting started`_
- `Forex getting started`_
- `Crypto getting started`_

===========
Init client
===========
.. automethod:: polygon.WebSocketClient.__init__
  :noindex:

============================
Connect
============================
.. automethod:: polygon.WebSocketClient.connect

============================
Run
============================
.. automethod:: polygon.WebSocketClient.run

============================
Subscribe
============================
.. automethod:: polygon.WebSocketClient.subscribe

============================
Unsubscribe
============================
.. automethod:: polygon.WebSocketClient.unsubscribe

============================
Close
============================
.. automethod:: polygon.WebSocketClient.close

.. _Stocks getting started: https://polygon.io/docs/stocks/ws_getting-started
.. _Options getting started: https://polygon.io/docs/options/ws_getting-started
.. _Forex getting started: https://polygon.io/docs/forex/ws_getting-started
.. _Crypto getting started: https://polygon.io/docs/crypto/ws_getting-started