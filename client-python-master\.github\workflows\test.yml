name: unittest
on:
  push:
    tags:
      - v*
    branches:
      - v1
  pull_request:
permissions:
  contents: read
jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest]
        python-version: ['3.8', '3.9', '3.10']
    runs-on: ${{ matrix.os }}
    name: ${{ matrix.os }} Unit test ${{ matrix.python-version }}
    steps:
      - name: Windows hack
        run: git config --system core.longpaths true
        if: ${{ matrix.os == 'windows-latest' }}
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}
      - name: Setup Poetry
        uses: abatilo/actions-poetry@v2
      - name: Install pypi deps
        run: poetry install
      - name: Unit tests
        run: make test
