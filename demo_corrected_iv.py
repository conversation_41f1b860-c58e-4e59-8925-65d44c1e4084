"""
Demonstration of Corrected IV Fluctuations
Shows the fixed HV vs IV chart with proper daily IV variations
"""

import os
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from datetime import datetime, timedelta
from hv_vs_iv_analyzer import HVvsIVAnalyzer

def create_demo_chart():
    print("📊 Creating Corrected HV vs IV Chart Demo")
    print("=" * 50)
    
    # Force demo mode to show enhanced IV fluctuations
    analyzer = HVvsIVAnalyzer()
    analyzer.real_data = False  # Force demo mode for better visualization
    
    symbol = "AAPL"
    
    # Get price data (this will be real from your API)
    os.environ['POLYGON_API_KEY'] = "********************************"
    real_analyzer = HVvsIVAnalyzer()
    prices = real_analyzer.get_historical_prices(symbol, days=120)
    
    print(f"✅ Got {len(prices)} days of real price data for {symbol}")
    
    # Calculate HV (real calculation)
    hv = analyzer.calculate_historical_volatility(prices['close'], 20)
    print(f"✅ Calculated 20-day HV for {len(hv)} days")
    
    # Generate enhanced IV with proper fluctuations
    iv = analyzer._generate_mock_iv(symbol, prices)
    print(f"✅ Generated enhanced IV with daily fluctuations")
    
    # Align data
    common_dates = hv.index.intersection(iv.index)
    hv_aligned = hv.loc[common_dates]
    iv_aligned = iv.loc[common_dates]
    
    # Create the corrected chart
    fig = go.Figure()
    
    # Historical Volatility line
    fig.add_trace(go.Scatter(
        x=hv_aligned.index,
        y=hv_aligned.values,
        mode='lines',
        name='20-Day Historical Volatility',
        line=dict(color='blue', width=2),
        hovertemplate='<b>HV:</b> %{y:.2f}%<br><b>Date:</b> %{x}<extra></extra>'
    ))
    
    # Corrected Implied Volatility line (now with daily fluctuations)
    fig.add_trace(go.Scatter(
        x=iv_aligned.index,
        y=iv_aligned.values,
        mode='lines',
        name='ATM Implied Volatility (20 DTE Equivalent)',
        line=dict(color='red', width=2),
        hovertemplate='<b>IV:</b> %{y:.2f}%<br><b>Date:</b> %{x}<extra></extra>'
    ))
    
    # Add shaded regions for IV > HV and IV < HV
    fig.add_trace(go.Scatter(
        x=hv_aligned.index.tolist() + hv_aligned.index.tolist()[::-1],
        y=iv_aligned.values.tolist() + hv_aligned.values.tolist()[::-1],
        fill='tonexty',
        fillcolor='rgba(255, 0, 0, 0.1)',
        line=dict(color='rgba(255,255,255,0)'),
        name='IV > HV (Options Expensive)',
        showlegend=True,
        hoverinfo='skip'
    ))
    
    # Calculate metrics
    current_hv = hv_aligned.iloc[-1]
    current_iv = iv_aligned.iloc[-1]
    iv_hv_ratio = current_iv / current_hv
    
    # Update layout
    fig.update_layout(
        title=f'✅ CORRECTED: 20-Day Historical vs. ATM Implied Volatility - {symbol}<br><sub>IV now shows proper daily fluctuations | Current IV/HV Ratio: {iv_hv_ratio:.2f}</sub>',
        xaxis_title='Date',
        yaxis_title='Volatility Percentage (%)',
        template='plotly_dark',
        height=600,
        hovermode='x unified',
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        )
    )
    
    # Add annotations for current values
    fig.add_annotation(
        x=hv_aligned.index[-1],
        y=current_hv,
        text=f"HV: {current_hv:.1f}%",
        showarrow=True,
        arrowhead=2,
        arrowcolor="blue",
        bgcolor="blue",
        bordercolor="white",
        font=dict(color="white")
    )
    
    fig.add_annotation(
        x=iv_aligned.index[-1],
        y=current_iv,
        text=f"IV: {current_iv:.1f}%",
        showarrow=True,
        arrowhead=2,
        arrowcolor="red",
        bgcolor="red",
        bordercolor="white",
        font=dict(color="white")
    )
    
    # Show statistics
    iv_changes = iv_aligned.diff().abs()
    hv_changes = hv_aligned.diff().abs()
    
    print(f"\n📈 Corrected Results:")
    print(f"HV Range: {hv_aligned.min():.1f}% - {hv_aligned.max():.1f}%")
    print(f"IV Range: {iv_aligned.min():.1f}% - {iv_aligned.max():.1f}%")
    print(f"HV Daily Volatility: {hv_aligned.std():.2f}%")
    print(f"IV Daily Volatility: {iv_aligned.std():.2f}%")
    print(f"IV Days with >0.1% change: {(iv_changes > 0.1).sum()}/{len(iv_aligned)}")
    print(f"Average IV/HV Ratio: {(iv_aligned / hv_aligned).mean():.2f}")
    
    # Generate trading signal
    if iv_hv_ratio > 1.2:
        signal = "🔴 SELL OPTIONS (IV > HV)"
    elif iv_hv_ratio < 0.8:
        signal = "🟢 BUY OPTIONS (IV < HV)"
    else:
        signal = "🟡 NEUTRAL (IV ≈ HV)"
    
    print(f"\n🎯 Current Trading Signal: {signal}")
    print(f"Current IV/HV Ratio: {iv_hv_ratio:.2f}")
    
    # Show the chart
    fig.show()
    
    # Show recent daily changes
    print(f"\n📅 Last 10 Days - Daily IV Changes:")
    print("Date          HV      IV    Change  IV/HV")
    print("-" * 45)
    for i in range(max(0, len(hv_aligned)-10), len(hv_aligned)):
        date = hv_aligned.index[i]
        hv_val = hv_aligned.iloc[i]
        iv_val = iv_aligned.iloc[i]
        change = iv_changes.iloc[i] if i > 0 else 0
        ratio = iv_val / hv_val
        print(f"{date.strftime('%Y-%m-%d')}  {hv_val:5.1f}%  {iv_val:5.1f}%  {change:+5.2f}%  {ratio:5.2f}")

if __name__ == "__main__":
    create_demo_chart()
