# PowerShell script to test all programming languages
# Run this to verify all languages are working correctly

Write-Host "🚀 Testing All Programming Languages for LLM Development" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

$testResults = @()

# Test Python
Write-Host "`n1. Testing Python..." -ForegroundColor Yellow
try {
    python hello.py
    $testResults += "✅ Python: PASSED"
} catch {
    $testResults += "❌ Python: FAILED - $_"
}

# Test JavaScript (Node.js)
Write-Host "`n2. Testing JavaScript (Node.js)..." -ForegroundColor Yellow
try {
    node hello.js
    $testResults += "✅ JavaScript: PASSED"
} catch {
    $testResults += "❌ JavaScript: FAILED - $_"
}

# Test TypeScript
Write-Host "`n3. Testing TypeScript..." -ForegroundColor Yellow
try {
    npx ts-node hello.ts
    $testResults += "✅ TypeScript: PASSED"
} catch {
    $testResults += "❌ TypeScript: FAILED - $_"
}

# Test Java
Write-Host "`n4. Testing Java..." -ForegroundColor Yellow
try {
    javac Hello.java
    java Hello
    Remove-Item Hello.class -ErrorAction SilentlyContinue
    $testResults += "✅ Java: PASSED"
} catch {
    $testResults += "❌ Java: FAILED - $_"
}

# Test Go
Write-Host "`n5. Testing Go..." -ForegroundColor Yellow
try {
    go run hello.go
    $testResults += "✅ Go: PASSED"
} catch {
    $testResults += "❌ Go: FAILED - $_"
}

# Test Rust
Write-Host "`n6. Testing Rust..." -ForegroundColor Yellow
try {
    rustc hello.rs -o hello_rust.exe
    .\hello_rust.exe
    Remove-Item hello_rust.exe -ErrorAction SilentlyContinue
    $testResults += "✅ Rust: PASSED"
} catch {
    $testResults += "❌ Rust: FAILED - $_"
}

# Test C#
Write-Host "`n7. Testing C#..." -ForegroundColor Yellow
try {
    dotnet run --project . --file hello.cs
    $testResults += "✅ C#: PASSED"
} catch {
    try {
        csc hello.cs
        .\hello.exe
        Remove-Item hello.exe -ErrorAction SilentlyContinue
        $testResults += "✅ C#: PASSED"
    } catch {
        $testResults += "❌ C#: FAILED - $_"
    }
}

# Test C
Write-Host "`n8. Testing C..." -ForegroundColor Yellow
try {
    gcc hello.c -o hello_c.exe
    .\hello_c.exe
    Remove-Item hello_c.exe -ErrorAction SilentlyContinue
    $testResults += "✅ C: PASSED"
} catch {
    $testResults += "❌ C: FAILED - $_"
}

# Test Ruby
Write-Host "`n9. Testing Ruby..." -ForegroundColor Yellow
try {
    ruby hello.rb
    $testResults += "✅ Ruby: PASSED"
} catch {
    $testResults += "❌ Ruby: FAILED - $_"
}

# Test PHP
Write-Host "`n10. Testing PHP..." -ForegroundColor Yellow
try {
    C:\php\php.exe hello.php
    $testResults += "✅ PHP: PASSED"
} catch {
    $testResults += "❌ PHP: FAILED - $_"
}

# Summary
Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "🎯 TEST RESULTS SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

foreach ($result in $testResults) {
    if ($result.Contains("PASSED")) {
        Write-Host $result -ForegroundColor Green
    } else {
        Write-Host $result -ForegroundColor Red
    }
}

$passedCount = ($testResults | Where-Object { $_.Contains("PASSED") }).Count
$totalCount = $testResults.Count

Write-Host "`n🏆 OVERALL: $passedCount/$totalCount languages working correctly!" -ForegroundColor Cyan

if ($passedCount -eq $totalCount) {
    Write-Host "🎉 Perfect! All languages are ready for LLM development!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Some languages need attention. Check the errors above." -ForegroundColor Yellow
}
