#!/usr/bin/env python3
"""
Demo launcher for the Hunting Anomalies tutorial
Creates sample data and runs the complete workflow
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import subprocess
import sys

def create_sample_data():
    """Create sample CSV data for demonstration"""
    print("📊 Creating sample data for demonstration...")
    
    # Create aggregates_day directory if it doesn't exist
    os.makedirs("aggregates_day", exist_ok=True)
    
    # Generate sample data for 30 days
    start_date = datetime.now() - timedelta(days=30)
    
    # Sample tickers
    tickers = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA", "AMZN", "META", "NFLX", "AMD", "INTC"]
    
    for day in range(30):
        current_date = start_date + timedelta(days=day)
        
        # Create daily data
        daily_data = []
        
        for ticker in tickers:
            # Generate realistic-looking data
            np.random.seed(hash(ticker + str(day)) % 1000)
            
            base_price = {"AAPL": 150, "MSFT": 300, "GOOGL": 2500, "TSLA": 200, 
                         "NVDA": 400, "AMZN": 3000, "META": 250, "NFLX": 400,
                         "AMD": 100, "INTC": 50}[ticker]
            
            # Add some price movement
            price_change = np.random.normal(0, 0.02)
            close_price = base_price * (1 + price_change)
            
            # Generate transaction count with some anomalies
            base_transactions = np.random.lognormal(8, 1)
            
            # Add anomalies for certain conditions
            if day > 20 and ticker in ["TSLA", "NVDA"] and np.random.random() > 0.7:
                base_transactions *= np.random.uniform(3, 8)  # Create anomaly
            
            transactions = int(base_transactions)
            
            # Create window_start timestamp (nanoseconds)
            window_start = int(current_date.timestamp() * 1_000_000_000)
            
            daily_data.append({
                "ticker": ticker,
                "window_start": window_start,
                "transactions": transactions,
                "close": close_price,
                "open": close_price * np.random.uniform(0.98, 1.02),
                "high": close_price * np.random.uniform(1.0, 1.05),
                "low": close_price * np.random.uniform(0.95, 1.0),
                "volume": transactions * np.random.randint(50, 200)
            })
        
        # Save daily CSV
        df = pd.DataFrame(daily_data)
        filename = f"aggregates_day/{current_date.strftime('%Y-%m-%d')}.csv"
        df.to_csv(filename, index=False)
        print(f"  ✅ Created {filename}")
    
    print(f"📁 Sample data created in aggregates_day/ directory")
    return current_date.strftime('%Y-%m-%d')  # Return latest date

def run_workflow():
    """Run the complete anomaly detection workflow"""
    print("\n🔍 Running Anomaly Detection Workflow")
    print("=" * 50)
    
    # Step 1: Create sample data
    latest_date = create_sample_data()
    
    # Step 2: Build lookup table
    print("\n📋 Step 1: Building lookup table...")
    try:
        result = subprocess.run([sys.executable, "build-lookup-table.py"], 
                              capture_output=True, text=True, check=True)
        print("✅ Lookup table built successfully!")
        if result.stdout:
            print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error building lookup table: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return
    
    # Step 3: Query for anomalies
    print(f"\n🔍 Step 2: Querying anomalies for {latest_date}...")
    try:
        result = subprocess.run([sys.executable, "query-lookup-table.py", latest_date], 
                              capture_output=True, text=True, check=True)
        print("✅ Anomaly query completed!")
        if result.stdout:
            print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error querying anomalies: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return
    
    # Step 4: Launch GUI
    print(f"\n🌐 Step 3: Launching web interface...")
    print(f"🔗 Open your browser to: http://localhost:8888")
    print(f"📊 You can explore anomalies for date: {latest_date}")
    print(f"⏹️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        subprocess.run([sys.executable, "gui-lookup-table.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped gracefully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running GUI: {e}")

def main():
    print("🎯 Hunting Anomalies Demo Launcher")
    print("Based on Polygon.io's tutorial")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("build-lookup-table.py"):
        print("❌ Error: Please run this script from the hunting-anomalies directory")
        print("💡 Make sure you're in the directory containing build-lookup-table.py")
        return
    
    # Check Python version
    if sys.version_info < (3, 8):
        print(f"❌ Error: Python 3.8+ required, you have {sys.version}")
        return
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Install requirements
    print("\n📦 Installing required packages...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pandas", "polygon-api-client"], 
                      check=True, capture_output=True)
        print("✅ Packages installed!")
    except subprocess.CalledProcessError:
        print("⚠️ Some packages may already be installed")
    
    # Run the workflow
    run_workflow()

if __name__ == "__main__":
    main()
