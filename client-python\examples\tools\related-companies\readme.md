# See Connections with the Related Companies API

This repository contains the Python script and HTML file used in our tutorial to demonstrate how to identify and visualize relationships between companies using Polygon.io's Related Companies API. The tutorial showcases how to fetch related company data and create a dynamic network graph using Python and vis.js, providing insights into the interconnected corporate landscape.

![Related Companies](./related-companies.png)

Please see the [tutorial](https://polygon.io/blog/related-companies-api) for more details.

### Prerequisites

- Python 3.8+
- Have Polygon.io's [python client](https://github.com/polygon-io/client-python) installed
- An active Polygon.io account with an API key

### Repository Contents

- `related-companies-demo.py`: Python script to fetch and process data from the Related Companies API.
- `index.html`: HTML file for visualizing the data as a network graph using vis.js.

### Running the Example

To run the Python script, ensure you have Python installed and your API key ready. Execute the following command:

```
python related-companies-demo.py
```

The script will generate a `data.json` file, which contains the nodes and edges for the network graph.

To visualize the relationships:

1. Take the `nodes` and `edges` from the `data.json` file and replace them in the `index.html` file
2. Open `index.html` in your web browser.
3. The web page should display the network graph.

For a complete step-by-step guide on setting up and exploring the capabilities of the Related Companies API, refer to our detailed [tutorial](https://polygon.io/blog/related-companies-api).