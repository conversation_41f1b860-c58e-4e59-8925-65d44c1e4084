"""
Check Polygon.io API Access and Available Data
Tests what historical options data is available with your plan
"""

import os
from polygon import RES<PERSON>lient
from datetime import datetime, timedelta

def check_api_access():
    api_key = "HiMX39xMvM40yIECd8sHGgenvpVDi5AZ"
    client = RESTClient(api_key)
    
    print("🔍 Checking Polygon.io API Access")
    print("=" * 50)
    
    # Test 1: Basic stock data
    try:
        print("📊 Testing stock data access...")
        aggs = list(client.list_aggs("AAPL", 1, "day", "2024-06-01", "2024-06-07", limit=5))
        print(f"✅ Stock data: {len(aggs)} days retrieved")
    except Exception as e:
        print(f"❌ Stock data error: {e}")
    
    # Test 2: Current options chain
    try:
        print("\n📈 Testing current options chain...")
        target_date = (datetime.now() + timedelta(days=20)).strftime("%Y-%m-%d")
        options = list(client.list_snapshot_options_chain(
            "AAPL",
            params={
                "expiration_date.gte": target_date,
                "limit": 5
            }
        ))
        print(f"✅ Current options: {len(options)} contracts")
        
        if options:
            opt = options[0]
            print(f"   Sample option: {opt.details.ticker if hasattr(opt, 'details') else 'N/A'}")
            if hasattr(opt, 'implied_volatility') and opt.implied_volatility:
                print(f"   Has IV data: {opt.implied_volatility:.4f}")
            else:
                print("   ❌ No IV data in current options")
                
    except Exception as e:
        print(f"❌ Current options error: {e}")
    
    # Test 3: Historical options data
    try:
        print("\n📅 Testing historical options data...")
        
        # Try different historical endpoints
        historical_date = "2024-06-01"
        
        # Method 1: Historical options chain
        try:
            historical_options = list(client.list_options_contracts(
                underlying_ticker="AAPL",
                contract_type="call",
                expiration_date=historical_date,
                limit=5
            ))
            print(f"✅ Historical contracts: {len(historical_options)} found")
        except Exception as e:
            print(f"❌ Historical contracts error: {e}")
        
        # Method 2: Try historical options quotes
        try:
            # This would be the endpoint for historical IV if available
            print("   Checking historical options quotes endpoint...")
            # Note: This endpoint typically requires higher tier access
            print("   ⚠️ Historical IV typically requires Starter+ plan")
        except Exception as e:
            print(f"   ❌ Historical quotes error: {e}")
            
    except Exception as e:
        print(f"❌ Historical options error: {e}")
    
    # Test 4: Check plan limits
    try:
        print("\n🔑 Checking API plan information...")
        
        # Make a few calls to see rate limits
        calls_made = 0
        for i in range(3):
            try:
                list(client.list_aggs("AAPL", 1, "day", "2024-06-01", "2024-06-01", limit=1))
                calls_made += 1
            except Exception as e:
                if "rate limit" in str(e).lower():
                    print(f"   ⚠️ Rate limit hit after {calls_made} calls")
                    break
                
        print(f"   ✅ Made {calls_made} successful calls")
        
    except Exception as e:
        print(f"❌ Plan check error: {e}")
    
    # Test 5: Try specific historical IV endpoint
    try:
        print("\n🎯 Testing specific historical IV access...")
        
        # Try to get historical options data for a specific date
        historical_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
        
        # This is the endpoint that would have historical IV
        try:
            # Polygon's historical options endpoint
            print(f"   Checking options data for {historical_date}...")
            
            # Try the snapshot endpoint for historical date
            # Note: This typically requires higher tier plans
            print("   ⚠️ Historical options snapshots require premium access")
            print("   💡 Your plan likely includes:")
            print("      - Real-time stock data ✅")
            print("      - Current options chains ✅") 
            print("      - Historical stock data ✅")
            print("      - Historical options IV ❌ (Premium feature)")
            
        except Exception as e:
            print(f"   ❌ Historical IV endpoint error: {e}")
            
    except Exception as e:
        print(f"❌ Historical IV test error: {e}")
    
    print(f"\n📋 Summary:")
    print(f"Your API key appears to have access to:")
    print(f"✅ Real-time stock prices")
    print(f"✅ Historical stock prices") 
    print(f"✅ Current options chains with IV")
    print(f"❌ Historical options IV (requires premium plan)")
    print(f"\n💡 For true historical IV, you'd need Polygon's Starter+ plan")
    print(f"🔗 https://polygon.io/pricing")

if __name__ == "__main__":
    check_api_access()
