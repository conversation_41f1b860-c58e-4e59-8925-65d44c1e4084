.. _enums_header:

Enums
==============================================================

==============================================================
Sort
==============================================================
.. autoclass:: polygon.rest.models.Sort
    :members:
    :undoc-members:                                  

==============================================================
Order
==============================================================
.. autoclass:: polygon.rest.models.Order
    :members:
    :undoc-members: 

==============================================================
Locale
==============================================================
.. autoclass:: polygon.rest.models.Locale
    :members:
    :undoc-members: 

==============================================================
Market
==============================================================
.. autoclass:: polygon.rest.models.Market
    :members:
    :undoc-members:

==============================================================
AssetClass
==============================================================
.. autoclass:: polygon.rest.models.AssetClass
    :members:
    :undoc-members: 

==============================================================
DividendType
==============================================================
.. autoclass:: polygon.rest.models.DividendType
    :members:
    :undoc-members: 

==============================================================
Frequency
==============================================================
.. autoclass:: polygon.rest.models.Frequency
    :members:
    :undoc-members: 

==============================================================
DataType
==============================================================
.. autoclass:: polygon.rest.models.DataType
    :members:
    :undoc-members: 

==============================================================
SIP
==============================================================
.. autoclass:: polygon.rest.models.SIP
    :members:
    :undoc-members: 

==============================================================
ExchangeType
==============================================================
.. autoclass:: polygon.rest.models.ExchangeType
    :members:
    :undoc-members: 

==============================================================
Direction
==============================================================
.. autoclass:: polygon.rest.models.Direction
    :members:
    :undoc-members: 

==============================================================
SnapshotMarketType
==============================================================
.. autoclass:: polygon.rest.models.SnapshotMarketType
    :members:
    :undoc-members: 

==============================================================
Timeframe
==============================================================
.. autoclass:: polygon.rest.models.Timeframe
    :members:
    :undoc-members: 

==============================================================
Precision
==============================================================
.. autoclass:: polygon.rest.models.Precision
    :members:
    :undoc-members: 
