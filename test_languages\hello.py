#!/usr/bin/env python3
"""
Python Hello World - The #1 LLM Language
Features: AI/ML, Data Science, Web Development, Automation
"""

def main():
    print("🐍 Hello from Python 3.12.7!")
    print("✨ Perfect for: AI/ML, Data Science, Web APIs, Automation")
    
    # Demonstrate Python features
    languages = ["Python", "JavaScript", "TypeScript", "Java", "Go", "Rust", "C#", "C/C++", "Ruby", "PHP"]
    print(f"📚 You have {len(languages)} languages installed:")
    
    for i, lang in enumerate(languages, 1):
        print(f"  {i}. {lang}")
    
    # List comprehension example
    modern_langs = [lang for lang in languages if lang in ["Python", "TypeScript", "Go", "Rust"]]
    print(f"🚀 Modern languages: {', '.join(modern_langs)}")

if __name__ == "__main__":
    main()
