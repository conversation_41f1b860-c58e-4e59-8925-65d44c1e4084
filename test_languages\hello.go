package main

import (
	"fmt"
	"strings"
)

/**
 * Go Hello World - Modern Systems Language
 * Features: Concurrency, Fast Compilation, Cloud Services
 */

type Language struct {
	Name     string
	Category string
	Year     int
}

func main() {
	fmt.Println("🚀 Hello from Go 1.24.4!")
	fmt.Println("✨ Perfect for: Cloud Services, Microservices, DevOps Tools")

	// Demonstrate Go features
	languages := []Language{
		{"Python", "AI/Data Science", 1991},
		{"JavaScript", "Web", 1995},
		{"TypeScript", "Web", 2012},
		{"Java", "Enterprise", 1995},
		{"Go", "Systems", 2009},
		{"Rust", "Systems", 2010},
		{"C#", "Enterprise", 2000},
		{"Ruby", "Web", 1995},
		{"PHP", "Web", 1995},
	}

	fmt.Printf("📚 You have %d languages installed:\n", len(languages))

	for i, lang := range languages {
		fmt.Printf("  %d. %s (%s, %d)\n", i+1, lang.Name, lang.Category, lang.Year)
	}

	// Slice filtering
	var cloudLanguages []string
	for _, lang := range languages {
		if contains([]string{"Go", "Python", "Java", "C#"}, lang.Name) {
			cloudLanguages = append(cloudLanguages, lang.Name)
		}
	}

	fmt.Printf("☁️ Cloud languages: %s\n", strings.Join(cloudLanguages, ", "))

	// Goroutine example (concurrency)
	done := make(chan bool)
	go func() {
		fmt.Println("⚡ Go has built-in concurrency with goroutines!")
		done <- true
	}()
	<-done
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
