"""
Interactive HV vs IV Analyzer with Gradio UI
Professional options volatility analysis tool
"""

import gradio as gr
import pandas as pd
from hv_vs_iv_analyzer import HVvsIVAnalyzer

# Initialize the analyzer
analyzer = HVvsIVAnalyzer()

def analyze_symbol(symbol, days_back=180):
    """Analyze a single symbol and return chart + summary"""
    try:
        symbol = symbol.upper().strip()
        if not symbol:
            return None, "Please enter a valid symbol"
        
        fig, summary = analyzer.create_hv_iv_chart(symbol)
        return fig, summary
        
    except Exception as e:
        return None, f"❌ Error analyzing {symbol}: {str(e)}"

def compare_symbols(symbols_text):
    """Compare multiple symbols"""
    try:
        symbols = [s.strip().upper() for s in symbols_text.split(',') if s.strip()]
        
        if len(symbols) < 2:
            return "Please enter at least 2 symbols separated by commas"
        
        comparison_df = analyzer.analyze_multiple_symbols(symbols)
        
        # Format the results nicely
        if len(comparison_df) > 0:
            formatted_df = comparison_df.round(2)
            
            # Add color coding for signals
            def color_signal(val):
                if val == 'BUY':
                    return '🟢 BUY'
                elif val == 'SELL':
                    return '🔴 SELL'
                else:
                    return '🟡 NEUTRAL'
            
            formatted_df['Signal'] = formatted_df['Signal'].apply(color_signal)
            
            return formatted_df
        else:
            return "No data available for the provided symbols"
            
    except Exception as e:
        return f"❌ Error: {str(e)}"

# Create Gradio interface
with gr.Blocks(
    theme=gr.themes.Soft(primary_hue="blue").set(
        body_background_fill="*neutral_950",
        block_background_fill="*neutral_900",
        input_background_fill="*neutral_800"
    ),
    title="HV vs IV Options Analyzer"
) as app:
    
    gr.Markdown("""
    # 📊 Historical vs Implied Volatility Analyzer
    *Professional options trading analysis with real Polygon.io data*
    
    Compare 20-day Historical Volatility with ATM Implied Volatility to find trading opportunities.
    """)
    
    with gr.Tab("📈 Single Symbol Analysis"):
        with gr.Row():
            with gr.Column(scale=1):
                symbol_input = gr.Textbox(
                    label="📊 Stock Symbol",
                    value="AAPL",
                    placeholder="Enter symbol (AAPL, SPY, TSLA, etc.)",
                    info="Enter any valid stock ticker"
                )
                
                days_slider = gr.Slider(
                    minimum=60, maximum=365, value=180, step=30,
                    label="📅 Analysis Period (Days)",
                    info="Historical data lookback period"
                )
                
                analyze_btn = gr.Button("🚀 Analyze Volatility", variant="primary", size="lg")
            
            with gr.Column(scale=2):
                summary_output = gr.Markdown()
        
        volatility_chart = gr.Plot(label="📊 HV vs IV Chart")
        
        analyze_btn.click(
            analyze_symbol,
            inputs=[symbol_input, days_slider],
            outputs=[volatility_chart, summary_output]
        )
        
        # Auto-analyze on symbol change
        symbol_input.submit(
            analyze_symbol,
            inputs=[symbol_input, days_slider],
            outputs=[volatility_chart, summary_output]
        )
    
    with gr.Tab("🔍 Multi-Symbol Comparison"):
        with gr.Row():
            with gr.Column():
                symbols_input = gr.Textbox(
                    label="📈 Symbols (comma-separated)",
                    value="AAPL,SPY,TSLA,NVDA,MSFT",
                    placeholder="AAPL,SPY,TSLA,NVDA,MSFT",
                    info="Enter multiple symbols separated by commas"
                )
                
                compare_btn = gr.Button("📊 Compare Volatilities", variant="primary")
            
            with gr.Column():
                comparison_output = gr.Dataframe(
                    label="📋 Volatility Comparison",
                    headers=["Symbol", "HV (20d)", "IV (ATM)", "IV/HV Ratio", "Signal"],
                    wrap=True
                )
        
        compare_btn.click(
            compare_symbols,
            inputs=[symbols_input],
            outputs=[comparison_output]
        )
    
    with gr.Tab("📚 How It Works"):
        gr.Markdown("""
        ## 🎯 Understanding HV vs IV Analysis
        
        ### **Historical Volatility (HV)**
        - **What:** Actual price movement over past 20 trading days
        - **Calculation:** Standard deviation of daily log returns, annualized
        - **Formula:** `std(ln(Price_t / Price_t-1)) × √252`
        
        ### **Implied Volatility (IV)**
        - **What:** Market's expectation of future volatility
        - **Source:** ATM options with ~20 days to expiration
        - **Selection:** Closest strike to current price, delta ≈ 0.5
        
        ### **Trading Signals**
        
        | IV/HV Ratio | Signal | Interpretation |
        |-------------|--------|----------------|
        | > 1.2 | 🔴 **SELL OPTIONS** | Options expensive vs historical movement |
        | 0.8 - 1.2 | 🟡 **NEUTRAL** | Options fairly valued |
        | < 0.8 | 🟢 **BUY OPTIONS** | Options cheap vs historical movement |
        
        ### **Key Insights**
        - **High IV periods:** Often around earnings, news events
        - **IV > HV:** Market expects more volatility than historically seen
        - **IV < HV:** Market expects less volatility than recent past
        - **Mean reversion:** IV and HV tend to converge over time
        
        ### **Data Sources**
        - **Price Data:** Real-time from Polygon.io API
        - **Options Data:** Live ATM implied volatility
        - **Calculations:** Professional-grade methodology
        """)
    
    data_source = "🟢 Live Polygon.io Data" if analyzer.real_data else "🔴 Demo Mode"
    
    gr.Markdown(f"""
    ---
    **Data Source:** {data_source}
    
    **Professional Features:**
    - ✅ Real-time HV calculation (20-day rolling)
    - ✅ ATM IV from live options market
    - ✅ Professional trading signals
    - ✅ Multi-symbol comparison
    - ✅ Interactive charts with annotations
    
    *This tool provides the same analysis used by professional options traders*
    """)

if __name__ == "__main__":
    # Load with the first symbol
    print("🚀 Launching HV vs IV Analyzer...")
    
    app.launch(
        share=True,
        server_name="0.0.0.0",
        server_port=None,  # Auto-find port
        show_error=True
    )
