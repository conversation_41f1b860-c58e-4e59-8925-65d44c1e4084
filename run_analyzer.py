#!/usr/bin/env python3
"""
Quick launcher for the Options Flow Analyzer
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_options_analyzer.txt"])
        print("✅ Packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False

def check_api_key():
    """Check if Polygon API key is set"""
    api_key = os.getenv('POLYGON_API_KEY')
    if api_key:
        print(f"🟢 Polygon API key found: {api_key[:8]}...")
        return True
    else:
        print("🔴 No Polygon API key found. Using demo mode.")
        print("💡 To use real data, set POLYGON_API_KEY environment variable:")
        print("   Windows: set POLYGON_API_KEY=your_key_here")
        print("   Mac/Linux: export POLYGON_API_KEY=your_key_here")
        return False

def main():
    print("🎯 Options Flow Analyzer Launcher")
    print("=" * 40)

    # Check API key
    check_api_key()
    print()

    # Install requirements
    if not install_requirements():
        return
    print()

    # Launch the analyzer
    print("🚀 Launching Options Flow Analyzer...")
    print("📱 The app will open in your browser automatically")
    print("🔗 If it doesn't open, go to: http://localhost:7860")
    print()
    print("Press Ctrl+C to stop the application")
    print("=" * 40)

    try:
        # Import and run the analyzer
        from real_options_analyzer import app
        app.launch(
            share=True,
            server_name="0.0.0.0",
            server_port=7860,
            show_error=True,
            inbrowser=True  # Auto-open browser
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down gracefully...")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Try running: pip install -r requirements_options_analyzer.txt")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
