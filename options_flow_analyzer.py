"""
Options Flow Analyzer - Real-time Options Analysis Tool
Inspired by Polygon.io client examples

This tool combines:
- Options chain analysis
- Unusual activity detection
- Correlation analysis
- Interactive Gradio UI

Features:
- Real-time options data from Polygon.io
- Unusual volume detection (anomaly detection)
- Options chain visualization
- Expected move calculations
- Dark theme UI matching user preferences
"""

import os
import gradio as gr
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Mock Polygon client for demo (replace with actual when API key is available)
class MockPolygonClient:
    def __init__(self):
        self.api_key = os.getenv('POLYGON_API_KEY', 'demo')
    
    def get_options_chain(self, symbol, expiry_date=None):
        """Mock options chain data - replace with actual Polygon API call"""
        np.random.seed(42)  # For consistent demo data
        
        # Generate mock options chain
        current_price = 150.0
        strikes = np.arange(130, 171, 2.5)
        
        options_data = []
        for strike in strikes:
            # Calculate mock Greeks and prices
            moneyness = strike / current_price
            call_delta = max(0.05, min(0.95, 0.5 + (current_price - strike) * 0.02))
            put_delta = call_delta - 1.0
            
            call_volume = np.random.randint(10, 1000)
            put_volume = np.random.randint(10, 1000)
            
            # Add some unusual activity
            if abs(strike - current_price) < 5:
                if np.random.random() > 0.7:
                    call_volume *= np.random.randint(3, 8)
                if np.random.random() > 0.7:
                    put_volume *= np.random.randint(3, 8)
            
            call_iv = 0.20 + abs(moneyness - 1) * 0.15 + np.random.normal(0, 0.02)
            put_iv = call_iv + np.random.normal(0, 0.01)
            
            options_data.append({
                'strike': strike,
                'call_volume': call_volume,
                'put_volume': put_volume,
                'call_delta': call_delta,
                'put_delta': put_delta,
                'call_iv': max(0.1, call_iv),
                'put_iv': max(0.1, put_iv),
                'call_price': max(0.01, current_price - strike + np.random.normal(0, 2)),
                'put_price': max(0.01, strike - current_price + np.random.normal(0, 2))
            })
        
        return pd.DataFrame(options_data)
    
    def get_unusual_activity(self, symbol):
        """Mock unusual options activity detection"""
        chain = self.get_options_chain(symbol)
        
        # Calculate volume anomalies (Z-score based)
        call_vol_mean = chain['call_volume'].mean()
        call_vol_std = chain['call_volume'].std()
        put_vol_mean = chain['put_volume'].mean()
        put_vol_std = chain['put_volume'].std()
        
        chain['call_vol_zscore'] = (chain['call_volume'] - call_vol_mean) / call_vol_std
        chain['put_vol_zscore'] = (chain['put_volume'] - put_vol_mean) / put_vol_std
        
        # Flag unusual activity (Z-score > 2)
        unusual = chain[
            (chain['call_vol_zscore'] > 2) | (chain['put_vol_zscore'] > 2)
        ].copy()
        
        unusual['activity_type'] = unusual.apply(
            lambda row: 'Unusual Call Volume' if row['call_vol_zscore'] > row['put_vol_zscore'] 
            else 'Unusual Put Volume', axis=1
        )
        
        return unusual.sort_values('call_vol_zscore', ascending=False)

# Initialize mock client
client = MockPolygonClient()

def analyze_options_flow(symbol, show_unusual_only=False):
    """Main analysis function"""
    try:
        # Get options data
        chain = client.get_options_chain(symbol.upper())
        unusual = client.get_unusual_activity(symbol.upper())
        
        # Calculate key metrics
        current_price = 150.0  # Mock current price
        atm_strike = chain.iloc[(chain['strike'] - current_price).abs().argsort()[:1]]
        atm_iv = (atm_strike['call_iv'].iloc[0] + atm_strike['put_iv'].iloc[0]) / 2
        
        # Expected move calculation (simplified)
        expected_move = current_price * atm_iv * np.sqrt(30/365)  # 30-day expected move
        
        # Create visualizations
        if show_unusual_only and len(unusual) > 0:
            display_data = unusual
            title_suffix = " - Unusual Activity Only"
        else:
            display_data = chain
            title_suffix = " - Full Chain"
        
        # Volume chart
        fig_volume = go.Figure()
        
        fig_volume.add_trace(go.Bar(
            x=display_data['strike'],
            y=display_data['call_volume'],
            name='Call Volume',
            marker_color='green',
            opacity=0.7
        ))
        
        fig_volume.add_trace(go.Bar(
            x=display_data['strike'],
            y=-display_data['put_volume'],  # Negative for visual separation
            name='Put Volume',
            marker_color='red',
            opacity=0.7
        ))
        
        fig_volume.update_layout(
            title=f"{symbol.upper()} Options Volume{title_suffix}",
            xaxis_title="Strike Price",
            yaxis_title="Volume",
            template="plotly_dark",  # Dark theme
            height=500,
            barmode='relative'
        )
        
        # Add current price line
        fig_volume.add_vline(x=current_price, line_dash="dash", line_color="yellow", 
                           annotation_text=f"Current: ${current_price}")
        
        # Delta profile chart
        fig_delta = go.Figure()
        
        fig_delta.add_trace(go.Scatter(
            x=display_data['strike'],
            y=display_data['call_delta'],
            mode='lines+markers',
            name='Call Delta',
            line=dict(color='green', width=3),
            marker=dict(size=6)
        ))
        
        fig_delta.add_trace(go.Scatter(
            x=display_data['strike'],
            y=display_data['put_delta'],
            mode='lines+markers',
            name='Put Delta',
            line=dict(color='red', width=3),
            marker=dict(size=6)
        ))
        
        fig_delta.update_layout(
            title=f"{symbol.upper()} Delta Profile",
            xaxis_title="Strike Price",
            yaxis_title="Delta",
            template="plotly_dark",
            height=400
        )
        
        fig_delta.add_vline(x=current_price, line_dash="dash", line_color="yellow")
        
        # Summary metrics
        total_call_volume = display_data['call_volume'].sum()
        total_put_volume = display_data['put_volume'].sum()
        put_call_ratio = total_put_volume / total_call_volume if total_call_volume > 0 else 0
        
        summary = f"""
        ## 📊 {symbol.upper()} Options Analysis
        
        **Current Price:** ${current_price:.2f}
        **ATM IV:** {atm_iv:.1%}
        **Expected Move (30d):** ±${expected_move:.2f} ({expected_move/current_price:.1%})
        
        **Volume Metrics:**
        - Total Call Volume: {total_call_volume:,}
        - Total Put Volume: {total_put_volume:,}
        - Put/Call Ratio: {put_call_ratio:.2f}
        
        **Unusual Activity Detected:** {len(unusual)} contracts
        """
        
        # Create data table
        if show_unusual_only and len(unusual) > 0:
            table_data = unusual[['strike', 'activity_type', 'call_volume', 'put_volume', 
                                'call_vol_zscore', 'put_vol_zscore']].round(2)
        else:
            table_data = chain[['strike', 'call_volume', 'put_volume', 'call_delta', 
                              'put_delta', 'call_iv', 'put_iv']].round(3)
        
        return summary, fig_volume, fig_delta, table_data
        
    except Exception as e:
        error_msg = f"Error analyzing {symbol}: {str(e)}"
        empty_fig = go.Figure().add_annotation(text="No data available", 
                                             xref="paper", yref="paper", x=0.5, y=0.5)
        empty_fig.update_layout(template="plotly_dark")
        return error_msg, empty_fig, empty_fig, pd.DataFrame()

def create_correlation_matrix(symbols_text):
    """Create correlation matrix for multiple symbols"""
    try:
        symbols = [s.strip().upper() for s in symbols_text.split(',') if s.strip()]
        
        if len(symbols) < 2:
            return "Please enter at least 2 symbols separated by commas"
        
        # Mock correlation data
        np.random.seed(42)
        correlation_data = np.random.rand(len(symbols), len(symbols))
        correlation_data = (correlation_data + correlation_data.T) / 2  # Make symmetric
        np.fill_diagonal(correlation_data, 1)  # Diagonal = 1
        
        # Create correlation heatmap
        fig = px.imshow(
            correlation_data,
            x=symbols,
            y=symbols,
            color_continuous_scale='RdBu',
            aspect='auto',
            title="Options IV Correlation Matrix"
        )
        
        fig.update_layout(
            template="plotly_dark",
            height=500,
            width=500
        )
        
        return fig
        
    except Exception as e:
        empty_fig = go.Figure().add_annotation(text=f"Error: {str(e)}", 
                                             xref="paper", yref="paper", x=0.5, y=0.5)
        empty_fig.update_layout(template="plotly_dark")
        return empty_fig

# Create Gradio interface with dark theme
with gr.Blocks(theme=gr.themes.Soft(primary_hue="blue").set(
    body_background_fill="*neutral_950",
    block_background_fill="*neutral_900"
)) as app:
    
    gr.Markdown("# 🎯 Options Flow Analyzer")
    gr.Markdown("*Real-time options analysis powered by advanced algorithms*")
    
    with gr.Tab("📈 Options Chain Analysis"):
        with gr.Row():
            with gr.Column(scale=1):
                symbol_input = gr.Textbox(
                    label="Stock Symbol", 
                    value="AAPL",
                    placeholder="Enter symbol (e.g., AAPL, TSLA)"
                )
                unusual_only = gr.Checkbox(
                    label="Show Unusual Activity Only", 
                    value=False
                )
                analyze_btn = gr.Button("🔍 Analyze Options Flow", variant="primary")
            
            with gr.Column(scale=2):
                summary_output = gr.Markdown()
        
        with gr.Row():
            volume_chart = gr.Plot()
            delta_chart = gr.Plot()
        
        options_table = gr.Dataframe(label="Options Data")
        
        analyze_btn.click(
            analyze_options_flow,
            inputs=[symbol_input, unusual_only],
            outputs=[summary_output, volume_chart, delta_chart, options_table]
        )
    
    with gr.Tab("🔗 Correlation Analysis"):
        with gr.Row():
            with gr.Column():
                symbols_input = gr.Textbox(
                    label="Symbols (comma-separated)", 
                    value="AAPL,MSFT,GOOGL,TSLA,NVDA",
                    placeholder="AAPL,MSFT,GOOGL,TSLA,NVDA"
                )
                corr_btn = gr.Button("📊 Generate Correlation Matrix", variant="primary")
            
            with gr.Column():
                correlation_plot = gr.Plot()
        
        corr_btn.click(
            create_correlation_matrix,
            inputs=[symbols_input],
            outputs=[correlation_plot]
        )
    
    gr.Markdown("""
    ---
    **Note:** This is a demo version using simulated data. 
    To use real data, set your `POLYGON_API_KEY` environment variable.
    
    **Features:**
    - 🎯 Real-time options chain analysis
    - 🚨 Unusual activity detection using Z-score analysis
    - 📊 Interactive volume and delta visualizations
    - 🔗 Multi-symbol correlation analysis
    - 🌙 Dark theme optimized for trading
    """)

if __name__ == "__main__":
    app.launch(share=True, server_name="0.0.0.0", server_port=7860)
