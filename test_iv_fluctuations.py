"""
Test script to demonstrate the corrected IV fluctuations
Shows before/after comparison of IV calculation
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from hv_vs_iv_analyzer import HVvsIVAnalyzer

def test_iv_fluctuations():
    print("🧪 Testing IV Fluctuation Corrections")
    print("=" * 50)
    
    # Set API key
    os.environ['POLYGON_API_KEY'] = "********************************"
    
    analyzer = HVvsIVAnalyzer()
    
    # Test with AAPL
    symbol = "AAPL"
    print(f"📊 Testing {symbol} IV fluctuations...")
    
    # Get price data
    prices = analyzer.get_historical_prices(symbol, days=60)
    print(f"✅ Got {len(prices)} days of price data")
    
    # Calculate HV
    hv = analyzer.calculate_historical_volatility(prices['close'], 20)
    print(f"✅ Calculated HV for {len(hv)} days")
    
    # Get corrected IV (with daily fluctuations)
    iv = analyzer.get_atm_implied_volatility(symbol, prices)
    print(f"✅ Generated IV for {len(iv)} days")
    
    # Align data
    common_dates = hv.index.intersection(iv.index)
    hv_aligned = hv.loc[common_dates]
    iv_aligned = iv.loc[common_dates]
    
    # Show statistics
    print(f"\n📈 Results Summary:")
    print(f"HV Range: {hv_aligned.min():.1f}% - {hv_aligned.max():.1f}%")
    print(f"IV Range: {iv_aligned.min():.1f}% - {iv_aligned.max():.1f}%")
    print(f"HV Std Dev: {hv_aligned.std():.2f}%")
    print(f"IV Std Dev: {iv_aligned.std():.2f}%")
    print(f"Average IV/HV Ratio: {(iv_aligned / hv_aligned).mean():.2f}")
    
    # Check for flatline (old bug)
    iv_changes = iv_aligned.diff().abs()
    hv_changes = hv_aligned.diff().abs()
    
    print(f"\n🔍 Fluctuation Analysis:")
    print(f"IV Daily Changes > 0.1%: {(iv_changes > 0.1).sum()} days")
    print(f"HV Daily Changes > 0.1%: {(hv_changes > 0.1).sum()} days")
    print(f"IV Max Daily Change: {iv_changes.max():.2f}%")
    print(f"HV Max Daily Change: {hv_changes.max():.2f}%")
    
    if (iv_changes > 0.1).sum() > len(iv_aligned) * 0.3:
        print("✅ IV shows realistic daily fluctuations")
    else:
        print("❌ IV appears to be flatlining")
    
    # Show recent values
    print(f"\n📅 Last 5 Days:")
    print("Date          HV      IV    IV/HV")
    print("-" * 35)
    for i in range(max(0, len(hv_aligned)-5), len(hv_aligned)):
        date = hv_aligned.index[i]
        hv_val = hv_aligned.iloc[i]
        iv_val = iv_aligned.iloc[i]
        ratio = iv_val / hv_val
        print(f"{date.strftime('%Y-%m-%d')}  {hv_val:5.1f}%  {iv_val:5.1f}%  {ratio:5.2f}")

if __name__ == "__main__":
    test_iv_fluctuations()
