import lz4.frame  # type: ignore
import pickle
import datetime

"""
This script performs the following tasks:

1. Reads aggregated market data ('aggs') for a specific stock symbol for multiple dates.
2. Data is read from compressed (LZ4) and pickled files, which should have been generated by a separate data downloading script.
3. Displays the read data to the console.
4. Handles exceptions gracefully: informs the user if a file for a specific date was not found or if any other error occurred.

Usage:
1. pip install lz4
2. Ensure that the compressed '.pickle.lz4' files for the specified stock symbol and date range exist in the same directory as this script.
3. Modify the date range and stock symbol in the script as per your requirements.
4. Run the script.

The script will read and display the market data for each specified date and stock symbol.

Note: This script is designed to be compatible with files generated by a data downloading script, such as 'bulk_aggs_downloader.py'.
"""


def read_trades_for_date(symbol, date):
    """Reads trades for a given symbol and date, then prints them."""

    # Construct the filename, similar to your writer script
    filename = f"{symbol}-aggs-{date}.pickle.lz4"

    try:
        with open(filename, "rb") as file:
            compressed_data = file.read()
            trades = pickle.loads(lz4.frame.decompress(compressed_data))
            print(trades)
            return trades
    except FileNotFoundError:
        print(f"No file found for {date}")
    except Exception as e:
        print(f"An error occurred: {e}")


def main():
    start_date = datetime.date(2023, 8, 1)
    end_date = datetime.date(2023, 8, 31)
    symbol = "HCP"

    # Loop through each weekday between the start and end dates and read the trades
    day = start_date
    while day <= end_date:
        if day.weekday() < 5:  # 0-4 denotes Monday to Friday
            read_trades_for_date(symbol, day)
        day += datetime.timedelta(days=1)


if __name__ == "__main__":
    main()
