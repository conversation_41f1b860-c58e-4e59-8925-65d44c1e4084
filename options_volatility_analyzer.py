"""
Options Volatility Analyzer
Shows relationship between Historical Volatility and Implied Volatility
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def black_scholes_call(S, K, T, r, sigma):
    """
    Black-Scholes call option pricing formula
    S: Current stock price
    K: Strike price
    T: Time to expiration (years)
    r: Risk-free rate
    sigma: Volatility (as decimal)
    """
    from scipy.stats import norm
    
    d1 = (np.log(S/K) + (r + sigma**2/2)*T) / (sigma*np.sqrt(T))
    d2 = d1 - sigma*np.sqrt(T)
    
    call_price = S*norm.cdf(d1) - K*np.exp(-r*T)*norm.cdf(d2)
    return call_price

def calculate_hv_for_options(prices, periods=[10, 20, 30]):
    """
    Calculate HV for different periods commonly used in options
    """
    results = {}
    
    for period in periods:
        # Daily log returns
        returns = np.log(prices / prices.shift(1)).dropna()
        
        # Rolling standard deviation
        rolling_std = returns.rolling(window=period).std()
        
        # Annualize
        hv = rolling_std * np.sqrt(252) * 100
        
        results[f"HV_{period}d"] = hv.iloc[-1]
    
    return results

def volatility_trading_signals(hv_dict, current_iv):
    """
    Generate trading signals based on HV vs IV comparison
    """
    signals = {}
    
    for period, hv in hv_dict.items():
        iv_hv_ratio = current_iv / hv
        
        if iv_hv_ratio > 1.2:
            signal = "SELL OPTIONS (IV > HV)"
            reason = "Options are expensive relative to historical movement"
        elif iv_hv_ratio < 0.8:
            signal = "BUY OPTIONS (IV < HV)"
            reason = "Options are cheap relative to historical movement"
        else:
            signal = "NEUTRAL"
            reason = "IV and HV are fairly aligned"
        
        signals[period] = {
            'signal': signal,
            'reason': reason,
            'iv_hv_ratio': iv_hv_ratio
        }
    
    return signals

def demonstrate_volatility_impact():
    """
    Show how volatility changes affect option prices
    """
    print("💰 Volatility Impact on Option Prices")
    print("=" * 50)
    
    # Option parameters
    S = 100  # Current stock price
    K = 105  # Strike price (5% OTM)
    T = 30/365  # 30 days to expiration
    r = 0.05  # 5% risk-free rate
    
    volatilities = [0.15, 0.20, 0.25, 0.30, 0.35, 0.40]  # 15% to 40%
    
    print(f"Stock Price: ${S}")
    print(f"Strike Price: ${K}")
    print(f"Days to Expiry: 30")
    print(f"Risk-free Rate: {r*100}%")
    print()
    print(f"{'Volatility':<12}{'Call Price':<12}{'Price Change':<15}{'% Change'}")
    print("-" * 55)
    
    base_price = None
    for vol in volatilities:
        try:
            price = black_scholes_call(S, K, T, r, vol)
            
            if base_price is None:
                base_price = price
                change = 0
                pct_change = 0
            else:
                change = price - base_price
                pct_change = (change / base_price) * 100
            
            print(f"{vol*100:>8.0f}%    ${price:>8.2f}    ${change:>+8.2f}      {pct_change:>+6.1f}%")
        except:
            print(f"{vol*100:>8.0f}%    Error calculating")

def main():
    print("📊 Options Historical Volatility Analysis")
    print("=" * 50)
    
    # Create sample price data
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
    
    # Simulate stock with varying volatility
    returns = []
    for i in range(100):
        if 20 <= i <= 40:  # High volatility period
            daily_return = np.random.normal(0, 0.03)  # 3% daily vol
        else:  # Normal volatility
            daily_return = np.random.normal(0, 0.015)  # 1.5% daily vol
    
        returns.append(daily_return)
    
    # Calculate prices
    prices = [100]  # Start at $100
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    
    price_series = pd.Series(prices[1:], index=dates)
    
    # Calculate HV for different periods
    hv_results = calculate_hv_for_options(price_series)
    
    print("📈 Historical Volatility Analysis:")
    print("-" * 30)
    for period, hv in hv_results.items():
        print(f"{period}: {hv:.2f}%")
    
    # Simulate current implied volatility
    current_iv = 28.5  # Example IV from options market
    
    print(f"\n🎯 Current Market Data:")
    print(f"Current Stock Price: ${price_series.iloc[-1]:.2f}")
    print(f"Current Implied Volatility: {current_iv:.1f}%")
    
    # Generate trading signals
    signals = volatility_trading_signals(hv_results, current_iv)
    
    print(f"\n🚦 Trading Signals:")
    print("-" * 40)
    for period, signal_data in signals.items():
        print(f"\n{period}:")
        print(f"  Signal: {signal_data['signal']}")
        print(f"  IV/HV Ratio: {signal_data['iv_hv_ratio']:.2f}")
        print(f"  Reason: {signal_data['reason']}")
    
    # Show volatility impact on pricing
    print(f"\n")
    demonstrate_volatility_impact()
    
    print(f"\n📚 Key Concepts:")
    print("• Historical Volatility = Past actual price movement")
    print("• Implied Volatility = Market's expectation of future movement")
    print("• High HV periods often follow news events or earnings")
    print("• Options traders look for IV/HV discrepancies")
    print("• Volatility clustering: high vol periods tend to cluster")

if __name__ == "__main__":
    main()
