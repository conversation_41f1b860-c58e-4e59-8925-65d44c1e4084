"""
Historical Volatility Calculator for Options Trading
Shows exactly how HV is calculated step-by-step
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timed<PERSON><PERSON>

def calculate_historical_volatility(prices, window=30):
    """
    Calculate historical volatility using the standard method
    
    Args:
        prices: Series of stock prices
        window: Number of days to look back (default 30)
    
    Returns:
        Annualized historical volatility as percentage
    """
    
    # Step 1: Calculate daily logarithmic returns
    # ln(Price_today / Price_yesterday)
    log_returns = np.log(prices / prices.shift(1))
    
    # Step 2: Calculate rolling standard deviation
    rolling_std = log_returns.rolling(window=window).std()
    
    # Step 3: Annualize (252 trading days per year)
    annualized_vol = rolling_std * np.sqrt(252)
    
    # Convert to percentage
    return annualized_vol * 100

def calculate_hv_detailed(prices, window=30):
    """
    Detailed calculation showing each step
    """
    print(f"📊 Calculating {window}-day Historical Volatility")
    print("=" * 50)
    
    # Step 1: Daily Returns
    daily_returns = np.log(prices / prices.shift(1)).dropna()
    print(f"Step 1: Daily log returns calculated")
    print(f"Sample returns: {daily_returns.tail(5).values}")
    
    # Step 2: Standard Deviation
    std_dev = daily_returns.rolling(window=window).std()
    print(f"\nStep 2: {window}-day rolling standard deviation")
    print(f"Latest std dev: {std_dev.iloc[-1]:.6f}")
    
    # Step 3: Annualize
    annual_factor = np.sqrt(252)
    hv = std_dev * annual_factor * 100
    
    print(f"\nStep 3: Annualization")
    print(f"Annual factor (√252): {annual_factor:.4f}")
    print(f"Final HV: {hv.iloc[-1]:.2f}%")
    
    return hv

def compare_hv_periods(prices):
    """
    Compare different HV calculation periods
    """
    periods = [10, 20, 30, 60, 90]
    
    print("\n📈 Historical Volatility Comparison")
    print("=" * 40)
    print(f"{'Period':<10}{'HV %':<10}{'Description'}")
    print("-" * 40)
    
    for period in periods:
        hv = calculate_historical_volatility(prices, period)
        latest_hv = hv.iloc[-1]
        
        if period <= 20:
            desc = "Short-term"
        elif period <= 60:
            desc = "Medium-term"
        else:
            desc = "Long-term"
            
        print(f"{period:<10}{latest_hv:<10.2f}{desc}")

def create_sample_data():
    """
    Create sample stock price data for demonstration
    """
    np.random.seed(42)
    
    # Generate 100 days of sample prices
    days = 100
    initial_price = 100
    
    # Simulate realistic price movements
    returns = np.random.normal(0.001, 0.02, days)  # 0.1% daily return, 2% daily vol
    
    # Add some volatility clustering (realistic market behavior)
    for i in range(20, 40):  # High vol period
        returns[i] *= 2
    
    for i in range(60, 80):  # Another high vol period
        returns[i] *= 1.5
    
    # Calculate cumulative prices
    prices = [initial_price]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    
    # Create date index
    dates = pd.date_range(start='2024-01-01', periods=len(prices), freq='D')
    
    return pd.Series(prices, index=dates)

def main():
    print("🎯 Historical Volatility Calculator")
    print("Understanding Options Volatility")
    print("=" * 50)
    
    # Create sample data
    prices = create_sample_data()
    
    print(f"📊 Sample Data: {len(prices)} days of price data")
    print(f"Price range: ${prices.min():.2f} - ${prices.max():.2f}")
    
    # Calculate detailed HV
    hv_30 = calculate_hv_detailed(prices, window=30)
    
    # Compare different periods
    compare_hv_periods(prices)
    
    # Show the mathematical relationship
    print(f"\n🧮 Mathematical Breakdown:")
    print(f"Daily volatility: {hv_30.iloc[-1] / (np.sqrt(252) * 100):.4f}")
    print(f"Weekly volatility: {hv_30.iloc[-1] / np.sqrt(52):.2f}%")
    print(f"Monthly volatility: {hv_30.iloc[-1] / np.sqrt(12):.2f}%")
    print(f"Annual volatility: {hv_30.iloc[-1]:.2f}%")
    
    # Key insights
    print(f"\n💡 Key Insights:")
    print(f"• HV measures actual past price movement")
    print(f"• Higher HV = more price swings = higher option premiums")
    print(f"• Options traders compare HV vs Implied Volatility (IV)")
    print(f"• If IV > HV: Options may be expensive")
    print(f"• If IV < HV: Options may be cheap")

if __name__ == "__main__":
    main()
