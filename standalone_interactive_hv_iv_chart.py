"""
Standalone Interactive HV vs IV Chart
Professional-grade implementation with dynamic time period selection

Architecture:
- Plotly Dash for interactive web interface
- Efficient data caching and lazy loading
- Professional UI/UX design
- Production-ready error handling
"""

import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Import our production data engine
from production_hv_iv_analyzer import ProductionHVIVAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InteractiveHVIVChart:
    """
    Standalone interactive HV vs IV chart with dynamic time period selection
    Built for professional trading environments
    """

    def __init__(self, default_symbol: str = "AAPL"):
        self.default_symbol = default_symbol
        self.analyzer = ProductionHVIVAnalyzer(cache_enabled=True)
        self.data_cache = {}

        # Time period configurations
        self.time_periods = {
            "3 Months": 90,
            "6 Months": 180,
            "1 Year": 365,
            "2 Years": 730,
            "4 Years": 1460
        }

        logger.info(f"Interactive HV/IV Chart initialized for {default_symbol}")

    def get_data_for_period(self, symbol: str, period_days: int) -> Tuple[pd.Series, pd.Series, Dict]:
        """
        Get HV and IV data for specified time period with intelligent caching
        Returns: (hv_series, iv_series, metadata)
        """
        cache_key = f"{symbol}_{period_days}"

        if cache_key in self.data_cache:
            logger.info(f"Cache hit for {symbol} - {period_days} days")
            return self.data_cache[cache_key]

        try:
            logger.info(f"Loading data for {symbol} - {period_days} days")

            # Get price data
            prices = self.analyzer.get_stock_prices(symbol, days=period_days)

            # Calculate HV
            hv = self.analyzer.calculate_historical_volatility(prices['close'], window=20)

            # Get IV data
            iv_start = prices.index.min()
            iv_end = prices.index.max()
            iv = self.analyzer.get_historical_iv(symbol, iv_start, iv_end)

            # Align data
            common_dates = hv.index.intersection(iv.index)
            hv_aligned = hv.loc[common_dates]
            iv_aligned = iv.loc[common_dates]

            # Generate metadata
            metadata = {
                'symbol': symbol,
                'period_days': period_days,
                'data_points': len(hv_aligned),
                'date_range': (hv_aligned.index.min(), hv_aligned.index.max()),
                'current_hv': hv_aligned.iloc[-1] if len(hv_aligned) > 0 else 0,
                'current_iv': iv_aligned.iloc[-1] if len(iv_aligned) > 0 else 0,
                'hv_range': (hv_aligned.min(), hv_aligned.max()),
                'iv_range': (iv_aligned.min(), iv_aligned.max())
            }

            # Cache the results
            result = (hv_aligned, iv_aligned, metadata)
            self.data_cache[cache_key] = result

            logger.info(f"Data loaded: {len(hv_aligned)} points for {symbol}")
            return result

        except Exception as e:
            logger.error(f"Error loading data for {symbol}: {e}")
            # Return empty data
            empty_series = pd.Series(dtype=float)
            empty_metadata = {'symbol': symbol, 'error': str(e)}
            return empty_series, empty_series, empty_metadata

    def create_chart(self, symbol: str, period_name: str) -> go.Figure:
        """
        Create the interactive chart for specified time period
        """
        period_days = self.time_periods[period_name]
        hv, iv, metadata = self.get_data_for_period(symbol, period_days)

        # Create figure
        fig = go.Figure()

        if len(hv) > 0 and len(iv) > 0:
            # Historical Volatility line
            fig.add_trace(go.Scatter(
                x=hv.index,
                y=hv.values,
                mode='lines',
                name='20-Day Historical Volatility',
                line=dict(color='#2E86AB', width=2.5),
                hovertemplate='<b>HV:</b> %{y:.2f}%<br><b>Date:</b> %{x}<extra></extra>'
            ))

            # Implied Volatility line
            fig.add_trace(go.Scatter(
                x=iv.index,
                y=iv.values,
                mode='lines',
                name='ATM Implied Volatility (20 DTE)',
                line=dict(color='#A23B72', width=2.5),
                hovertemplate='<b>IV:</b> %{y:.2f}%<br><b>Date:</b> %{x}<extra></extra>'
            ))

            # Add fill between lines for visual enhancement
            fig.add_trace(go.Scatter(
                x=hv.index.tolist() + hv.index.tolist()[::-1],
                y=iv.values.tolist() + hv.values.tolist()[::-1],
                fill='tonexty',
                fillcolor='rgba(162, 59, 114, 0.1)',
                line=dict(color='rgba(255,255,255,0)'),
                name='IV vs HV Spread',
                showlegend=False,
                hoverinfo='skip'
            ))

            # Calculate current metrics for subtitle
            current_hv = metadata.get('current_hv', 0)
            current_iv = metadata.get('current_iv', 0)
            iv_hv_ratio = current_iv / current_hv if current_hv > 0 else 0

            subtitle = f"Current: HV {current_hv:.1f}% | IV {current_iv:.1f}% | Ratio {iv_hv_ratio:.2f}"

        else:
            # Error case - show empty chart with message
            fig.add_annotation(
                text="No data available for selected period",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=16, color="gray")
            )
            subtitle = "Data unavailable"

        # Professional styling
        fig.update_layout(
            title=dict(
                text=f'{symbol} - Historical vs Implied Volatility Over Time<br><sub>{period_name} View | {subtitle}</sub>',
                x=0.5,
                font=dict(size=20, family="Arial, sans-serif")
            ),
            xaxis=dict(
                title='Date',
                showgrid=True,
                gridcolor='rgba(128,128,128,0.2)',
                showline=True,
                linecolor='rgba(128,128,128,0.5)'
            ),
            yaxis=dict(
                title='Volatility Percentage (%)',
                showgrid=True,
                gridcolor='rgba(128,128,128,0.2)',
                showline=True,
                linecolor='rgba(128,128,128,0.5)'
            ),
            plot_bgcolor='white',
            paper_bgcolor='white',
            font=dict(family="Arial, sans-serif", size=12),
            hovermode='x unified',
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1,
                bgcolor="rgba(255,255,255,0.8)",
                bordercolor="rgba(128,128,128,0.5)",
                borderwidth=1
            ),
            height=600,
            margin=dict(l=60, r=60, t=100, b=60)
        )

        return fig

    def create_dash_app(self) -> dash.Dash:
        """
        Create the Dash application with professional UI
        """
        app = dash.Dash(__name__)

        # Professional CSS styling
        app.layout = html.Div([
            # Header section
            html.Div([
                html.H1(
                    "Interactive Historical vs Implied Volatility Analysis",
                    style={
                        'textAlign': 'center',
                        'color': '#2E86AB',
                        'fontFamily': 'Arial, sans-serif',
                        'marginBottom': '10px',
                        'fontSize': '28px'
                    }
                ),
                html.P(
                    "Professional-grade volatility analysis with dynamic time period selection",
                    style={
                        'textAlign': 'center',
                        'color': '#666',
                        'fontFamily': 'Arial, sans-serif',
                        'marginBottom': '30px',
                        'fontSize': '14px'
                    }
                )
            ], style={'backgroundColor': '#f8f9fa', 'padding': '20px', 'marginBottom': '20px'}),

            # Controls section
            html.Div([
                html.Div([
                    html.Label(
                        "Symbol:",
                        style={'fontWeight': 'bold', 'marginRight': '10px', 'fontFamily': 'Arial, sans-serif'}
                    ),
                    dcc.Input(
                        id='symbol-input',
                        value=self.default_symbol,
                        type='text',
                        style={
                            'marginRight': '20px',
                            'padding': '8px',
                            'border': '1px solid #ccc',
                            'borderRadius': '4px',
                            'fontFamily': 'Arial, sans-serif'
                        }
                    )
                ], style={'display': 'inline-block', 'marginRight': '30px'}),

                html.Div([
                    html.Label(
                        "Time Period:",
                        style={'fontWeight': 'bold', 'marginRight': '10px', 'fontFamily': 'Arial, sans-serif'}
                    ),
                    dcc.Dropdown(
                        id='period-dropdown',
                        options=[{'label': k, 'value': k} for k in self.time_periods.keys()],
                        value='6 Months',
                        style={
                            'width': '150px',
                            'fontFamily': 'Arial, sans-serif'
                        }
                    )
                ], style={'display': 'inline-block'})
            ], style={
                'textAlign': 'center',
                'marginBottom': '30px',
                'padding': '20px',
                'backgroundColor': '#f8f9fa',
                'borderRadius': '8px'
            }),

            # Chart section
            html.Div([
                dcc.Graph(
                    id='hv-iv-chart',
                    config={
                        'displayModeBar': True,
                        'displaylogo': False,
                        'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d']
                    }
                )
            ], style={'marginBottom': '20px'}),

            # Footer
            html.Div([
                html.P(
                    "Built with production-grade data processing • Real market data via Polygon.io",
                    style={
                        'textAlign': 'center',
                        'color': '#888',
                        'fontSize': '12px',
                        'fontFamily': 'Arial, sans-serif'
                    }
                )
            ], style={'padding': '20px'})

        ], style={
            'maxWidth': '1200px',
            'margin': '0 auto',
            'padding': '20px',
            'fontFamily': 'Arial, sans-serif'
        })

        # Callback for dynamic chart updates
        @app.callback(
            Output('hv-iv-chart', 'figure'),
            [Input('symbol-input', 'value'),
             Input('period-dropdown', 'value')]
        )
        def update_chart(symbol, period):
            """Update chart when symbol or period changes"""
            if not symbol:
                symbol = self.default_symbol

            symbol = symbol.upper().strip()

            try:
                return self.create_chart(symbol, period)
            except Exception as e:
                logger.error(f"Error updating chart: {e}")
                # Return empty chart on error
                fig = go.Figure()
                fig.add_annotation(
                    text=f"Error loading data for {symbol}: {str(e)}",
                    xref="paper", yref="paper",
                    x=0.5, y=0.5,
                    showarrow=False,
                    font=dict(size=16, color="red")
                )
                fig.update_layout(height=600)
                return fig

        return app

def main():
    """
    Launch the standalone interactive application
    """
    print("🚀 Launching Interactive HV vs IV Chart")
    print("=" * 50)

    # Initialize the chart application
    chart_app = InteractiveHVIVChart(default_symbol="AAPL")

    # Create Dash app
    app = chart_app.create_dash_app()

    # Launch the application
    print("📊 Starting interactive chart server...")
    print("🌐 Open your browser to: http://localhost:8050")
    print("⚡ Features:")
    print("   • Dynamic time period selection (3M, 6M, 1Y, 2Y, 4Y)")
    print("   • Real-time symbol switching")
    print("   • Professional interactive charts")
    print("   • Production-grade data processing")
    print("\n🔧 Controls:")
    print("   • Enter any stock symbol (AAPL, SPY, TSLA, etc.)")
    print("   • Select time period from dropdown")
    print("   • Chart updates automatically")
    print("\n⏹️  Press Ctrl+C to stop")
    print("=" * 50)

    try:
        app.run(
            debug=False,
            host='0.0.0.0',
            port=8050
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down gracefully...")
    except Exception as e:
        print(f"❌ Server error: {e}")

if __name__ == "__main__":
    main()
