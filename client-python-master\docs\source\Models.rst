.. _models_header:

Models
==============================================================

==============================================================
Universal Snapshot
==============================================================
.. autoclass:: polygon.rest.models.UniversalSnapshot

==============================================================
Universal Snapshot Session
==============================================================
.. autoclass:: polygon.rest.models.UniversalSnapshotSession

==============================================================
Universal Snapshot Last Quote
==============================================================
.. autoclass:: polygon.rest.models.UniversalSnapshotLastQuote

==============================================================
Universal Snapshot Last Trade
==============================================================
.. autoclass:: polygon.rest.models.UniversalSnapshotLastTrade

==============================================================
Universal Snapshot Details
==============================================================
.. autoclass:: polygon.rest.models.UniversalSnapshotDetails

==============================================================
Universal Snapshot Underlying Asset
==============================================================
.. autoclass:: polygon.rest.models.UniversalSnapshotUnderlyingAsset

==============================================================
Agg
==============================================================
.. autoclass:: polygon.rest.models.Agg

==============================================================
Grouped Daily Agg
==============================================================
.. autoclass:: polygon.rest.models.GroupedDailyAgg

==============================================================
Daily Open Close Agg
==============================================================
.. autoclass:: polygon.rest.models.DailyOpenCloseAgg

==============================================================
Previous Close Agg
==============================================================
.. autoclass:: polygon.rest.models.PreviousCloseAgg

==============================================================
Trade
==============================================================
.. autoclass:: polygon.rest.models.Trade

==============================================================
Last Trade
==============================================================
.. autoclass:: polygon.rest.models.LastTrade

==============================================================
Crypto Trade
==============================================================
.. autoclass:: polygon.rest.models.CryptoTrade

==============================================================
Quote
==============================================================
.. autoclass:: polygon.rest.models.Quote

==============================================================
Last Quote
==============================================================
.. autoclass:: polygon.rest.models.LastQuote

==============================================================
Snapshot Min
==============================================================
.. autoclass:: polygon.rest.models.MinuteSnapshot

==============================================================
Snapshot
==============================================================
.. autoclass:: polygon.rest.models.TickerSnapshot

==============================================================
Day Option Contract Snapshot
==============================================================
.. autoclass:: polygon.rest.models.DayOptionContractSnapshot

==============================================================
Option Details
==============================================================
.. autoclass:: polygon.rest.models.OptionDetails

==============================================================
Option Greeks
==============================================================
.. autoclass:: polygon.rest.models.Greeks

==============================================================
Underlying Asset
==============================================================
.. autoclass:: polygon.rest.models.UnderlyingAsset

==============================================================
Option Contract Snapshot
==============================================================
.. autoclass:: polygon.rest.models.OptionContractSnapshot

==============================================================
Order Book Quote
==============================================================
.. autoclass:: polygon.rest.models.OrderBookQuote

==============================================================
Snapshot Ticker Full Book
==============================================================
.. autoclass:: polygon.rest.models.SnapshotTickerFullBook

==============================================================
Ticker
==============================================================
.. autoclass:: polygon.rest.models.Ticker

==============================================================
Address
==============================================================
.. autoclass:: polygon.rest.models.CompanyAddress

==============================================================
Branding
==============================================================
.. autoclass:: polygon.rest.models.Branding

==============================================================
Publisher
==============================================================
.. autoclass:: polygon.rest.models.Publisher

==============================================================
Ticker Details
==============================================================
.. autoclass:: polygon.rest.models.TickerDetails

==============================================================
Ticker News
==============================================================
.. autoclass:: polygon.rest.models.TickerNews

==============================================================
Ticker Types
==============================================================
.. autoclass:: polygon.rest.models.TickerTypes

==============================================================
Market Holiday
==============================================================
.. autoclass:: polygon.rest.models.MarketHoliday

==============================================================
Market Currencies
==============================================================
.. autoclass:: polygon.rest.models.MarketCurrencies

==============================================================
Market Exchanges
==============================================================
.. autoclass:: polygon.rest.models.MarketExchanges

==============================================================
Market Status
==============================================================
.. autoclass:: polygon.rest.models.MarketStatus

==============================================================
Split
==============================================================
.. autoclass:: polygon.rest.models.Split

==============================================================
Dividend
==============================================================
.. autoclass:: polygon.rest.models.Dividend

==============================================================
Sip Mapping
==============================================================
.. autoclass:: polygon.rest.models.SipMapping

==============================================================
Consolidated
==============================================================
.. autoclass:: polygon.rest.models.Consolidated

==============================================================
Market Center
==============================================================
.. autoclass:: polygon.rest.models.MarketCenter

==============================================================
Update Rules
==============================================================
.. autoclass:: polygon.rest.models.UpdateRules

==============================================================
Condition
==============================================================
.. autoclass:: polygon.rest.models.Condition

==============================================================
Exchange
==============================================================
.. autoclass:: polygon.rest.models.Exchange
