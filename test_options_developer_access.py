"""
Test Options Developer Plan Access
Your plan should include Greeks, IV, & Open Interest
"""

import os
from polygon import RESTClient
from datetime import datetime, timedelta

def test_options_developer_plan():
    api_key = "HiMX39xMvM40yIECd8sHGgenvpVDi5AZ"
    client = RESTClient(api_key)
    
    print("🎯 Testing Options Developer Plan Access")
    print("Plan: $79/month - Should include Greeks, IV, & Open Interest")
    print("=" * 60)
    
    # Test 1: Options snapshot with IV
    try:
        print("📊 Testing options snapshot (should have IV)...")
        
        snapshot = client.get_snapshot_option("AAPL", "O:AAPL250620C00190000")
        print(f"✅ Got snapshot data")
        
        # Check for IV in snapshot
        if hasattr(snapshot, 'implied_volatility'):
            print(f"✅ IV found: {snapshot.implied_volatility}")
        else:
            print("❌ No IV in snapshot")
            print(f"Available fields: {dir(snapshot)}")
            
    except Exception as e:
        print(f"❌ Snapshot error: {e}")
    
    # Test 2: Options chain with detailed data
    try:
        print("\n📈 Testing detailed options chain...")
        
        target_date = (datetime.now() + timedelta(days=20)).strftime("%Y-%m-%d")
        
        for i, option in enumerate(client.list_snapshot_options_chain(
            "AAPL",
            params={
                "expiration_date.gte": target_date,
                "expiration_date.lte": (datetime.now() + timedelta(days=25)).strftime("%Y-%m-%d"),
                "limit": 3
            }
        )):
            print(f"\n--- Option {i+1}: {option.details.ticker if hasattr(option, 'details') else 'Unknown'} ---")
            
            # Check all available data
            print(f"Details available: {hasattr(option, 'details')}")
            print(f"Last quote: {hasattr(option, 'last_quote')}")
            print(f"Last trade: {hasattr(option, 'last_trade')}")
            print(f"Greeks: {hasattr(option, 'greeks')}")
            print(f"Implied volatility: {hasattr(option, 'implied_volatility')}")
            print(f"Open interest: {hasattr(option, 'open_interest')}")
            
            # Try to access IV data
            if hasattr(option, 'implied_volatility') and option.implied_volatility:
                print(f"✅ IV: {option.implied_volatility:.4f}")
            
            # Try to access Greeks
            if hasattr(option, 'greeks') and option.greeks:
                print(f"✅ Delta: {option.greeks.delta if hasattr(option.greeks, 'delta') else 'N/A'}")
                print(f"✅ Gamma: {option.greeks.gamma if hasattr(option.greeks, 'gamma') else 'N/A'}")
                print(f"✅ Theta: {option.greeks.theta if hasattr(option.greeks, 'theta') else 'N/A'}")
                print(f"✅ Vega: {option.greeks.vega if hasattr(option.greeks, 'vega') else 'N/A'}")
            
            # Try to access Open Interest
            if hasattr(option, 'open_interest') and option.open_interest:
                print(f"✅ Open Interest: {option.open_interest}")
            
            # Print all attributes to see what's available
            print(f"All attributes: {[attr for attr in dir(option) if not attr.startswith('_')]}")
            
            if i >= 2:  # Limit to 3 options
                break
                
    except Exception as e:
        print(f"❌ Options chain error: {e}")
    
    # Test 3: Historical options data
    try:
        print("\n📅 Testing historical options access...")
        
        # Try historical options quotes
        historical_date = (datetime.now() - timedelta(days=5)).strftime("%Y-%m-%d")
        
        try:
            # Test historical options endpoint
            print(f"Checking historical options for {historical_date}...")
            
            # Try to get historical options data
            historical_options = list(client.list_options_contracts(
                underlying_ticker="AAPL",
                contract_type="call",
                expiration_date_gte=historical_date,
                limit=5
            ))
            
            print(f"✅ Found {len(historical_options)} historical contracts")
            
            if historical_options:
                contract = historical_options[0]
                print(f"Sample contract: {contract.ticker}")
                
        except Exception as e:
            print(f"❌ Historical contracts error: {e}")
        
        # Try historical quotes endpoint
        try:
            print("Testing historical quotes endpoint...")
            
            # This should work with Options Developer plan
            option_ticker = "O:AAPL250620C00190000"
            
            quotes = list(client.list_quotes(
                option_ticker,
                timestamp_gte=historical_date,
                limit=5
            ))
            
            print(f"✅ Historical quotes: {len(quotes)} found")
            
        except Exception as e:
            print(f"❌ Historical quotes error: {e}")
            
    except Exception as e:
        print(f"❌ Historical options test error: {e}")
    
    # Test 4: Try different API endpoints
    try:
        print("\n🔍 Testing alternative endpoints...")
        
        # Try the aggregates endpoint for options
        try:
            option_ticker = "O:AAPL250620C00190000"
            aggs = list(client.list_aggs(
                option_ticker,
                1,
                "day",
                "2024-06-01",
                "2024-06-07",
                limit=5
            ))
            print(f"✅ Options aggregates: {len(aggs)} found")
            
        except Exception as e:
            print(f"❌ Options aggregates error: {e}")
            
    except Exception as e:
        print(f"❌ Alternative endpoints error: {e}")
    
    print(f"\n📋 Conclusion:")
    print(f"If you're not seeing IV/Greeks data, it might be:")
    print(f"1. ✅ Data is there but in different fields")
    print(f"2. ⚠️ Need to use different API endpoints")
    print(f"3. ❌ Plan not fully activated")
    print(f"4. 🔧 API client version issue")

if __name__ == "__main__":
    test_options_developer_plan()
