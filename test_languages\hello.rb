#!/usr/bin/env ruby
# Ruby Hello World - Elegant Web Development Language
# Features: Elegant Syntax, Web Frameworks (Rails), Rapid Development

class Language
  attr_accessor :name, :category, :year
  
  def initialize(name, category, year)
    @name = name
    @category = category
    @year = year
  end
  
  def to_s
    "#{@name} (#{@category}, #{@year})"
  end
end

def main
  puts "💎 Hello from Ruby 3.3.7!"
  puts "✨ Perfect for: Web Development (Rails), Scripting, Rapid Prototyping"
  
  # Demonstrate Ruby features
  languages = [
    Language.new("Python", "AI/Data Science", 1991),
    Language.new("JavaScript", "Web", 1995),
    Language.new("TypeScript", "Web", 2012),
    Language.new("Java", "Enterprise", 1995),
    Language.new("Go", "Systems", 2009),
    Language.new("Rust", "Systems", 2010),
    Language.new("C#", "Enterprise", 2000),
    Language.new("C/C++", "Systems", 1972),
    Language.new("Ruby", "Web", 1995),
    Language.new("PHP", "Web", 1995)
  ]
  
  puts "📚 You have #{languages.length} languages installed:"
  
  languages.each_with_index do |lang, index|
    puts "  #{index + 1}. #{lang}"
  end
  
  # Ruby's elegant filtering and mapping
  web_languages = languages.select { |lang| lang.category == "Web" }
                           .map(&:name)
  
  puts "🌐 Web languages: #{web_languages.join(", ")}"
  
  # Ruby blocks and iterators
  modern_count = languages.count { |lang| lang.year >= 2000 }
  puts "🚀 Modern languages (2000+): #{modern_count} out of #{languages.length}"
  
  # Ruby's expressive syntax
  puts "💎 Ruby makes programming enjoyable and productive!"
end

# Call main if this file is executed directly
main if __FILE__ == $0
