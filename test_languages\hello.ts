#!/usr/bin/env ts-node
/**
 * TypeScript Hello World - JavaScript with Types
 * Features: Type Safety, Modern JavaScript, Large-Scale Applications
 */

interface Language {
    name: string;
    category: string;
    year: number;
}

function main(): void {
    console.log("🔷 Hello from TypeScript 5.8.3!");
    console.log("✨ Perfect for: Large Apps, Type Safety, Modern Web Development");
    
    // Demonstrate TypeScript features with types
    const languages: Language[] = [
        { name: "Python", category: "AI/Data Science", year: 1991 },
        { name: "JavaScript", category: "Web", year: 1995 },
        { name: "TypeScript", category: "Web", year: 2012 },
        { name: "Java", category: "Enterprise", year: 1995 },
        { name: "Go", category: "Systems", year: 2009 },
        { name: "Rust", category: "Systems", year: 2010 },
        { name: "C#", category: "Enterprise", year: 2000 },
        { name: "Ruby", category: "Web", year: 1995 },
        { name: "PHP", category: "Web", year: 1995 }
    ];
    
    console.log(`📚 You have ${languages.length} languages installed:`);
    
    languages.forEach((lang: Language, index: number) => {
        console.log(`  ${index + 1}. ${lang.name} (${lang.category}, ${lang.year})`);
    });
    
    // Type-safe filtering
    const modernLanguages: Language[] = languages.filter((lang: Language) => lang.year >= 2000);
    console.log(`🚀 Modern languages (2000+): ${modernLanguages.map(l => l.name).join(", ")}`);
}

main();
