"""
Polygon.io Flat Files Setup for Historical Options IV
Uses your S3 credentials to access real historical options data
"""

import boto3
import pandas as pd
import gzip
import io
from datetime import datetime, timedelta
import os

class PolygonFlatFilesClient:
    def __init__(self):
        # Your Polygon flat files S3 credentials
        self.access_key = "75b025e4-91a9-4445-beae-052d4a499225"
        self.secret_key = "HiMX39xMvM40yIECd8sHGgenvpVDi5AZ"
        self.endpoint = "https://files.polygon.io"
        self.bucket = "flatfiles"
        
        # Initialize S3 client
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            endpoint_url=self.endpoint,
            region_name='us-east-1'  # Default region
        )
        
        print("✅ Connected to Polygon.io Flat Files")
    
    def list_available_data(self):
        """List available flat file data"""
        try:
            print("📁 Checking available flat files...")
            
            # List top-level directories
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket,
                Delimiter='/',
                MaxKeys=20
            )
            
            if 'CommonPrefixes' in response:
                print("Available data types:")
                for prefix in response['CommonPrefixes']:
                    print(f"  📂 {prefix['Prefix']}")
            
            # Check specifically for options data
            print("\n🎯 Checking for options data...")
            
            # Common paths for options data
            options_paths = [
                'us_options_opra/',
                'options/',
                'us_stocks_sip/options/',
                'derivatives/options/'
            ]
            
            for path in options_paths:
                try:
                    response = self.s3_client.list_objects_v2(
                        Bucket=self.bucket,
                        Prefix=path,
                        MaxKeys=5
                    )
                    
                    if 'Contents' in response:
                        print(f"✅ Found options data at: {path}")
                        for obj in response['Contents'][:3]:
                            print(f"   📄 {obj['Key']}")
                        break
                except:
                    continue
            
        except Exception as e:
            print(f"❌ Error listing files: {e}")
    
    def get_historical_options_iv(self, symbol, start_date, end_date):
        """Get historical options IV data from flat files"""
        try:
            print(f"📊 Fetching historical options IV for {symbol}")
            print(f"   Date range: {start_date} to {end_date}")
            
            # Try different possible paths for options data
            possible_paths = [
                f'us_options_opra/day_aggs_v1/{start_date.year}/{start_date.month:02d}/',
                f'options/day_aggs/{start_date.year}/{start_date.month:02d}/',
                f'derivatives/options/{start_date.year}/{start_date.month:02d}/'
            ]
            
            for base_path in possible_paths:
                try:
                    print(f"   Checking path: {base_path}")
                    
                    # List files in this path
                    response = self.s3_client.list_objects_v2(
                        Bucket=self.bucket,
                        Prefix=base_path,
                        MaxKeys=10
                    )
                    
                    if 'Contents' in response:
                        print(f"   ✅ Found {len(response['Contents'])} files")
                        
                        # Try to download and parse a sample file
                        for obj in response['Contents'][:2]:
                            file_key = obj['Key']
                            print(f"   📄 Trying file: {file_key}")
                            
                            try:
                                # Download file
                                file_obj = self.s3_client.get_object(
                                    Bucket=self.bucket,
                                    Key=file_key
                                )
                                
                                # Handle gzipped files
                                if file_key.endswith('.gz'):
                                    content = gzip.decompress(file_obj['Body'].read())
                                else:
                                    content = file_obj['Body'].read()
                                
                                # Try to parse as CSV
                                df = pd.read_csv(io.StringIO(content.decode('utf-8')), nrows=5)
                                
                                print(f"   ✅ Successfully parsed file")
                                print(f"   📊 Columns: {list(df.columns)}")
                                print(f"   📈 Sample data:")
                                print(df.head(2))
                                
                                # Check if this contains options data for our symbol
                                if 'ticker' in df.columns:
                                    symbol_data = df[df['ticker'].str.contains(symbol, na=False)]
                                    if len(symbol_data) > 0:
                                        print(f"   🎯 Found {len(symbol_data)} records for {symbol}")
                                        return df  # Return sample for now
                                
                                break  # Found valid data structure
                                
                            except Exception as e:
                                print(f"   ❌ Error parsing file {file_key}: {e}")
                                continue
                        
                        break  # Found valid path
                        
                except Exception as e:
                    print(f"   ❌ Error checking path {base_path}: {e}")
                    continue
            
            print("⚠️ No options data found in expected locations")
            return None
            
        except Exception as e:
            print(f"❌ Error fetching options data: {e}")
            return None
    
    def test_access(self):
        """Test flat files access"""
        print("🧪 Testing Flat Files Access")
        print("=" * 40)
        
        # Test basic connectivity
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket,
                MaxKeys=5
            )
            
            if 'Contents' in response:
                print(f"✅ Successfully connected to flat files")
                print(f"📁 Found {len(response['Contents'])} files in root")
                
                # Show sample files
                for obj in response['Contents'][:3]:
                    print(f"   📄 {obj['Key']}")
                
                return True
            else:
                print("❌ No files found in bucket")
                return False
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False

def main():
    print("🎯 Polygon.io Flat Files - Historical Options IV")
    print("=" * 50)
    
    # Initialize client
    client = PolygonFlatFilesClient()
    
    # Test access
    if client.test_access():
        print("\n📂 Exploring available data...")
        client.list_available_data()
        
        print("\n📊 Testing historical options data retrieval...")
        start_date = datetime(2024, 6, 1)
        end_date = datetime(2024, 6, 7)
        
        data = client.get_historical_options_iv("AAPL", start_date, end_date)
        
        if data is not None:
            print("✅ Successfully retrieved historical options data!")
        else:
            print("⚠️ Need to identify correct data structure")
    
    print(f"\n💡 Next steps:")
    print(f"1. Identify correct flat file structure for options IV")
    print(f"2. Parse historical options data")
    print(f"3. Extract ATM IV for each date")
    print(f"4. Create real HV vs IV charts")

if __name__ == "__main__":
    main()
