<?php
/**
 * PHP Hello World - Web Development Powerhouse
 * Features: Server-Side Web Development, Dynamic Content, CMS Development
 */

class Language {
    public $name;
    public $category;
    public $year;
    
    public function __construct($name, $category, $year) {
        $this->name = $name;
        $this->category = $category;
        $this->year = $year;
    }
    
    public function __toString() {
        return "{$this->name} ({$this->category}, {$this->year})";
    }
}

function main() {
    echo "🐘 Hello from PHP 8.3.22!\n";
    echo "✨ Perfect for: Web Development, CMS, Server-Side Applications\n";
    
    // Demonstrate PHP features
    $languages = [
        new Language("Python", "AI/Data Science", 1991),
        new Language("JavaScript", "Web", 1995),
        new Language("TypeScript", "Web", 2012),
        new Language("Java", "Enterprise", 1995),
        new Language("Go", "Systems", 2009),
        new Language("Rust", "Systems", 2010),
        new Language("C#", "Enterprise", 2000),
        new Language("C/C++", "Systems", 1972),
        new Language("Ruby", "Web", 1995),
        new Language("PHP", "Web", 1995)
    ];
    
    echo "📚 You have " . count($languages) . " languages installed:\n";
    
    foreach ($languages as $index => $lang) {
        echo "  " . ($index + 1) . ". $lang\n";
    }
    
    // PHP array filtering
    $webLanguages = array_filter($languages, function($lang) {
        return $lang->category === "Web";
    });
    
    $webNames = array_map(function($lang) {
        return $lang->name;
    }, $webLanguages);
    
    echo "🌐 Web languages: " . implode(", ", $webNames) . "\n";
    
    // Modern PHP features (PHP 8+)
    $modernLanguages = array_filter($languages, fn($lang) => $lang->year >= 2000);
    echo "🚀 Modern languages (2000+): " . count($modernLanguages) . " out of " . count($languages) . "\n";
    
    echo "🐘 PHP powers ~80% of the web including WordPress, Facebook, and Wikipedia!\n";
}

// Call main function
main();
?>
