# Pattern for Non-Blocking WebSocket and REST Calls in Python

This script demonstrates a non-blocking pattern for handling WebSocket streams and REST API calls in Python using asyncio. It focuses on efficient, concurrent processing of real-time financial data and asynchronous fetching of additional information, ensuring that real-time data streams are managed without delays or blockages. The tutorial provides both theoretical insights and a practical, adaptable example, ideal for applications in financial data processing and similar real-time data handling scenarios.

Please see the [tutorial](https://polygon.io/blog/pattern-for-non-blocking-websocket-and-rest-calls-in-python) for more details.

### Prerequisites

- Python 3.x
- Polygon.io account and Options API key

### Running the Example

```
python3 async_websocket_rest_handler.py
```