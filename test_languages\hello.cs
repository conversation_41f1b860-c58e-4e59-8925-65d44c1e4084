/**
 * C# Hello World - Modern .NET Language
 * Features: Object-Oriented, Cross-Platform, Enterprise Development
 */

using System;
using System.Collections.Generic;
using System.Linq;

namespace HelloWorld
{
    public class Language
    {
        public string Name { get; set; }
        public string Category { get; set; }
        public int Year { get; set; }

        public Language(string name, string category, int year)
        {
            Name = name;
            Category = category;
            Year = year;
        }

        public override string ToString()
        {
            return $"{Name} ({Category}, {Year})";
        }
    }

    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("🔷 Hello from C# (.NET 8.0.202)!");
            Console.WriteLine("✨ Perfect for: Enterprise Apps, Games, Cross-Platform Development");

            // Demonstrate C# features
            var languages = new List<Language>
            {
                new Language("Python", "AI/Data Science", 1991),
                new Language("JavaScript", "Web", 1995),
                new Language("TypeScript", "Web", 2012),
                new Language("Java", "Enterprise", 1995),
                new Language("Go", "Systems", 2009),
                new Language("Rust", "Systems", 2010),
                new Language("C#", "Enterprise", 2000),
                new Language("Ruby", "Web", 1995),
                new Language("PHP", "Web", 1995)
            };

            Console.WriteLine($"📚 You have {languages.Count} languages installed:");

            for (int i = 0; i < languages.Count; i++)
            {
                Console.WriteLine($"  {i + 1}. {languages[i]}");
            }

            // LINQ example (Language Integrated Query)
            var enterpriseLanguages = languages
                .Where(lang => lang.Category == "Enterprise")
                .Select(lang => lang.Name)
                .ToList();

            Console.WriteLine($"🏢 Enterprise languages: {string.Join(", ", enterpriseLanguages)}");

            // Modern C# features
            var modernLanguages = languages.Where(l => l.Year >= 2000).ToList();
            Console.WriteLine($"🚀 Modern languages (2000+): {string.Join(", ", modernLanguages.Select(l => l.Name))}");
        }
    }
}
